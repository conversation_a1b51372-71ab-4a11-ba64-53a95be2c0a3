"""
字段映射管理器 - 统一管理和验证字段映射
"""
import json
import os
import re
from typing import Dict, List, Optional, Any
from loguru import logger
import threading
from datetime import datetime


class FieldMappingManager:
    """字段映射管理器"""
    
    # 默认的系统隐藏字段
    DEFAULT_HIDDEN_FIELDS = [
        'id', 'created_at', 'updated_at', 
        'sequence_number', 'row_number', 'sequence'
    ]
    
    # 默认的字段类型
    DEFAULT_FIELD_TYPES = {
        # 基本信息字段
        'employee_id': 'string',
        'employee_name': 'string',
        'department': 'string',
        'employee_type': 'string',
        'employee_type_code': 'string',
        
        # 工资相关字段
        'position_salary_2025': 'float',
        'grade_salary_2025': 'float',
        'allowance': 'float',
        'balance_allowance': 'float',
        'basic_performance_2025': 'float',
        'performance_bonus_2025': 'float',
        'total_salary': 'float',
        
        # 补贴类字段
        'health_fee': 'float',
        'transport_allowance': 'float',
        'property_allowance': 'float',
        'housing_allowance': 'float',
        'car_allowance': 'float',
        'communication_allowance': 'float',
        
        # 其他金额字段
        'supplement': 'float',
        'advance': 'float',
        'provident_fund_2025': 'float',
        'pension_insurance': 'float',
        
        # 时间字段
        'year': 'year_string',
        'month': 'month_string',
        'created_at': 'date',
        'updated_at': 'date',
        
        # 系统字段
        'id': 'integer',
        'sequence': 'integer',
        'sequence_number': 'integer',
        'row_number': 'integer'
    }
    
    def __init__(self, config_path: str = 'state/data/field_mappings.json'):
        """
        初始化字段映射管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.logger = logger
        self._lock = threading.RLock()
        self._cache = {}
        self._config = None
        
        # 加载配置
        self._load_config()
        
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                    self.logger.info(f"🔧 [P3优化] 字段映射配置加载成功")
            else:
                self._config = self._create_default_config()
                self._save_config()
                self.logger.info(f"🔧 [P3优化] 创建默认字段映射配置")
                
            return self._config
            
        except Exception as e:
            self.logger.error(f"加载字段映射配置失败: {e}")
            self._config = self._create_default_config()
            return self._config
            
    def _create_default_config(self) -> Dict:
        """创建默认配置"""
        return {
            "version": "2.0",
            "last_updated": datetime.now().isoformat(),
            "global_settings": {
                "auto_generate_mappings": True,
                "enable_smart_suggestions": True,
                "save_edit_history": True,
                "preserve_chinese_headers": True
            },
            "table_mappings": {}
        }
        
    def _save_config(self):
        """保存配置文件"""
        try:
            with self._lock:
                # 确保目录存在
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                
                # 更新时间戳
                self._config["last_updated"] = datetime.now().isoformat()
                
                # 保存文件
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(self._config, f, ensure_ascii=False, indent=2)
                    
                self.logger.info("🔧 [P3优化] 字段映射配置已保存")
                
        except Exception as e:
            self.logger.error(f"保存字段映射配置失败: {e}")
            
    def ensure_table_mapping_complete(self, table_name: str) -> bool:
        """
        确保表的映射配置完整
        
        Args:
            table_name: 表名
            
        Returns:
            bool: 是否完整
        """
        try:
            with self._lock:
                if table_name not in self._config.get("table_mappings", {}):
                    # 创建新的表映射
                    self._create_table_mapping(table_name)
                    
                table_config = self._config["table_mappings"][table_name]
                
                # 确保有field_mappings
                if "field_mappings" not in table_config:
                    table_config["field_mappings"] = self._generate_default_mappings(table_name)
                    
                # 确保有field_types
                if "field_types" not in table_config:
                    table_config["field_types"] = self.DEFAULT_FIELD_TYPES.copy()
                    self.logger.info(f"🔧 [P3优化] 为表 {table_name} 添加默认字段类型")
                    
                # 确保有hidden_fields
                if "hidden_fields" not in table_config:
                    table_config["hidden_fields"] = self.DEFAULT_HIDDEN_FIELDS.copy()
                    self.logger.info(f"🔧 [P3优化] 为表 {table_name} 添加默认隐藏字段")
                    
                # 确保有display_order
                if "display_order" not in table_config:
                    table_config["display_order"] = self._generate_display_order(table_config["field_mappings"])
                    self.logger.info(f"🔧 [P3优化] 为表 {table_name} 生成显示顺序")
                    
                # 保存更新
                self._save_config()
                
                return True
                
        except Exception as e:
            self.logger.error(f"确保表映射完整性失败 {table_name}: {e}")
            return False
            
    def _create_table_mapping(self, table_name: str):
        """创建表映射"""
        self._config["table_mappings"][table_name] = {
            "field_mappings": self._generate_default_mappings(table_name),
            "field_types": self.DEFAULT_FIELD_TYPES.copy(),
            "hidden_fields": self.DEFAULT_HIDDEN_FIELDS.copy(),
            "display_order": [],
            "metadata": {
                "created_at": datetime.now().isoformat(),
                "table_type": self._detect_table_type(table_name)
            }
        }
        
    def _generate_default_mappings(self, table_name: str) -> Dict[str, str]:
        """生成默认映射"""
        # 基础映射
        mappings = {
            "employee_id": "工号",
            "employee_name": "姓名",
            "department": "部门名称",
            "employee_type": "人员类别",
            "employee_type_code": "人员类别代码",
            "position_salary_2025": "2025年岗位工资",
            "grade_salary_2025": "2025年薪级工资",
            "allowance": "津贴",
            "balance_allowance": "结余津贴",
            "basic_performance_2025": "2025年基础性绩效",
            "health_fee": "卫生费",
            "transport_allowance": "交通补贴",
            "property_allowance": "物业补贴",
            "housing_allowance": "住房补贴",
            "car_allowance": "车补",
            "communication_allowance": "通讯补贴",
            "performance_bonus_2025": "2025年奖励性绩效预发",
            "supplement": "补发",
            "advance": "借支",
            "total_salary": "应发工资",
            "provident_fund_2025": "2025公积金",
            "pension_insurance": "代扣代存养老保险",
            "year": "年份",
            "month": "月份",
            "created_at": "创建时间",
            "updated_at": "更新时间",
            "id": "自增主键",
            "sequence": "序号",
            "sequence_number": "序号",
            "row_number": "行号"
        }
        
        return mappings
        
    def _generate_display_order(self, field_mappings: Dict[str, str]) -> List[str]:
        """生成显示顺序"""
        # 定义优先显示的字段顺序
        priority_fields = [
            "employee_id", "employee_name", "department",
            "employee_type", "employee_type_code",
            "position_salary_2025", "grade_salary_2025",
            "allowance", "balance_allowance",
            "basic_performance_2025", "performance_bonus_2025",
            "total_salary"
        ]
        
        # 其他字段按字母顺序
        other_fields = [f for f in field_mappings.keys() 
                       if f not in priority_fields and f not in self.DEFAULT_HIDDEN_FIELDS]
        other_fields.sort()
        
        return priority_fields + other_fields
        
    def _detect_table_type(self, table_name: str) -> str:
        """检测表类型"""
        if "change_data" in table_name:
            return "change_table"
        elif "active" in table_name:
            return "active_employees"
        elif "retired" in table_name:
            return "retired_employees"
        elif "pension" in table_name:
            return "pension_employees"
        else:
            return "unknown"
            
    def get_field_mapping(self, table_name: str) -> Optional[Dict[str, str]]:
        """
        获取字段映射
        
        Args:
            table_name: 表名
            
        Returns:
            字段映射字典
        """
        try:
            # 确保映射完整
            self.ensure_table_mapping_complete(table_name)
            
            if table_name in self._config.get("table_mappings", {}):
                return self._config["table_mappings"][table_name].get("field_mappings", {})
                
            return None
            
        except Exception as e:
            self.logger.error(f"获取字段映射失败 {table_name}: {e}")
            return None
            
    def get_field_types(self, table_name: str) -> Dict[str, str]:
        """获取字段类型"""
        try:
            # 确保映射完整
            self.ensure_table_mapping_complete(table_name)
            
            if table_name in self._config.get("table_mappings", {}):
                return self._config["table_mappings"][table_name].get("field_types", {})
                
            return self.DEFAULT_FIELD_TYPES.copy()
            
        except Exception as e:
            self.logger.error(f"获取字段类型失败 {table_name}: {e}")
            return self.DEFAULT_FIELD_TYPES.copy()
            
    def get_hidden_fields(self, table_name: str) -> List[str]:
        """获取隐藏字段"""
        try:
            # 确保映射完整
            self.ensure_table_mapping_complete(table_name)
            
            if table_name in self._config.get("table_mappings", {}):
                return self._config["table_mappings"][table_name].get("hidden_fields", [])
                
            return self.DEFAULT_HIDDEN_FIELDS.copy()
            
        except Exception as e:
            self.logger.error(f"获取隐藏字段失败 {table_name}: {e}")
            return self.DEFAULT_HIDDEN_FIELDS.copy()
            
    def validate_mapping(self, table_name: str, data_columns: List[str]) -> Dict[str, Any]:
        """
        验证映射是否匹配实际数据
        
        Args:
            table_name: 表名
            data_columns: 实际数据列
            
        Returns:
            验证结果
        """
        try:
            result = {
                "is_valid": True,
                "missing_mappings": [],
                "unused_mappings": [],
                "suggestions": []
            }
            
            # 🔧 [P1-重要修复] 获取当前映射，确保格式正确
            mappings = self.get_field_mapping(table_name) or {}

            # 🔧 [P1-重要修复] 检查映射格式，确保键是数据库字段名
            if mappings:
                # 检查第一个键是否是数据库字段名格式（英文+下划线）
                first_key = next(iter(mappings.keys()))
                if not re.match(r'^[a-z_][a-z0-9_]*$', first_key):
                    # 如果不是数据库字段名格式，可能是旧格式，需要转换
                    self.logger.warning(f"🔧 [P1-重要修复] 检测到旧格式映射，表: {table_name}")
                    # 这里可以添加格式转换逻辑，暂时跳过验证
                    result["suggestions"].append("检测到旧格式字段映射，建议重新生成")
                    return result

            mapped_fields = set(mappings.keys())
            actual_fields = set(data_columns)

            # 找出缺失的映射
            missing = actual_fields - mapped_fields
            if missing:
                result["is_valid"] = False
                result["missing_mappings"] = list(missing)
                result["suggestions"].append(f"发现 {len(missing)} 个未映射字段")

            # 找出未使用的映射
            unused = mapped_fields - actual_fields
            if unused:
                # 排除系统字段
                unused_non_system = [f for f in unused if f not in self.DEFAULT_HIDDEN_FIELDS]
                if unused_non_system:
                    result["unused_mappings"] = unused_non_system
                    result["suggestions"].append(f"发现 {len(unused_non_system)} 个未使用映射")
                    
            return result

        except Exception as e:
            self.logger.error(f"验证映射失败 {table_name}: {e}")
            return {
                "is_valid": False,
                "error": str(e)
            }

    def convert_legacy_mapping_format(self, table_name: str, legacy_mapping: Dict[str, str]) -> Dict[str, str]:
        """
        🔧 [P1-重要修复] 转换旧格式映射为新格式

        Args:
            table_name: 表名
            legacy_mapping: 旧格式映射 {Excel列名: 数据库字段名}

        Returns:
            新格式映射 {数据库字段名: 显示名}
        """
        try:
            new_mapping = {}

            # 检查是否是旧格式（键是中文，值是英文）
            for key, value in legacy_mapping.items():
                # 如果键是中文，值是英文数据库字段名，则转换
                if re.match(r'.*[\u4e00-\u9fff].*', key) and re.match(r'^[a-z_][a-z0-9_]*$', value):
                    new_mapping[value] = key  # 交换键值
                    self.logger.debug(f"🔧 [P1-重要修复] 格式转换: {key} -> {value} => {value} -> {key}")
                else:
                    # 已经是新格式或其他情况，保持不变
                    new_mapping[key] = value

            self.logger.info(f"🔧 [P1-重要修复] 表 {table_name} 映射格式转换完成: {len(legacy_mapping)} -> {len(new_mapping)}")
            return new_mapping

        except Exception as e:
            self.logger.error(f"🔧 [P1-重要修复] 映射格式转换失败: {e}")
            return legacy_mapping

    def reload_mappings(self):
        """重新加载映射配置"""
        with self._lock:
            self._cache.clear()
            self._load_config()
            self.logger.info("🔧 [P3优化] 字段映射已重新加载")
            
    def clear_cache(self, table_name: Optional[str] = None):
        """清除缓存"""
        with self._lock:
            if table_name:
                self._cache.pop(table_name, None)
            else:
                self._cache.clear()
            self.logger.info(f"🔧 [P3优化] 缓存已清除: {table_name or '全部'}")