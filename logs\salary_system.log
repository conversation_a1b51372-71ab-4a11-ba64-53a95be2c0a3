2025-08-19 09:58:50.926 | INFO     | src.utils.log_config:_log_initialization_info:356 | 日志系统初始化完成
2025-08-19 09:58:50.926 | INFO     | src.utils.log_config:_log_initialization_info:357 | 日志级别: INFO
2025-08-19 09:58:50.942 | INFO     | src.utils.log_config:_log_initialization_info:358 | 控制台输出: True
2025-08-19 09:58:50.942 | INFO     | src.utils.log_config:_log_initialization_info:359 | 文件输出: True
2025-08-19 09:58:50.942 | INFO     | src.utils.log_config:_log_initialization_info:365 | 日志文件路径: logs/salary_system.log
2025-08-19 09:58:50.942 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-19 09:58:53.948 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-19 09:58:53.948 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-19 09:58:53.948 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-19 09:58:53.948 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-19 09:58:53.948 | INFO     | __main__:setup_app_logging:427 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-19 09:58:53.948 | INFO     | __main__:main:491 | 初始化核心管理器...
2025-08-19 09:58:53.948 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 09:58:53.948 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-19 09:58:53.948 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-19 09:58:53.948 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-19 09:58:53.948 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-19 09:58:53.979 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-19 09:58:53.979 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-19 09:58:53.979 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 09:58:53.995 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-19 09:58:53.995 | INFO     | __main__:main:496 | 核心管理器初始化完成。
2025-08-19 09:58:53.995 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-19 09:58:53.995 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-19 09:58:53.995 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 09:58:53.995 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-19 09:58:53.995 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-19 09:58:53.995 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-19 09:58:53.995 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-19 09:58:53.995 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11599 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-19 09:58:53.995 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 09:58:53.995 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11454 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-19 09:58:53.995 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11492 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-19 09:58:54.042 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-19 09:58:54.042 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-19 09:58:54.057 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 09:58:54.057 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:149 | 🔧 [配置修复] 创建了完整的默认配置，包含基本字段映射和模板
2025-08-19 09:58:54.057 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-19 09:58:54.057 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-19 09:58:54.057 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 09:58:54.057 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-19 09:58:54.057 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-19 09:58:54.057 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-19 09:58:54.057 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 09:58:54.057 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-19 09:58:54.057 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 15.7ms
2025-08-19 09:58:54.073 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-19 09:58:54.089 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-19 09:58:54.089 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-19 09:58:54.089 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-19 09:58:54.089 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-19 09:58:54.089 | INFO     | src.gui.prototype.prototype_main_window:__init__:3590 | 🚀 性能管理器已集成
2025-08-19 09:58:54.094 | INFO     | src.gui.prototype.prototype_main_window:__init__:3592 | ✅ 新架构集成成功！
2025-08-19 09:58:54.095 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3705 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-19 09:58:54.095 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3670 | ✅ 新架构事件监听器设置完成
2025-08-19 09:58:54.096 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-19 09:58:54.097 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-19 09:58:54.097 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-19 09:58:54.320 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2666 | 菜单栏创建完成
2025-08-19 09:58:54.320 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-19 09:58:54.320 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-19 09:58:54.320 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-19 09:58:54.320 | INFO     | src.gui.prototype.prototype_main_window:__init__:2641 | 菜单栏管理器初始化完成
2025-08-19 09:58:54.320 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-19 09:58:54.336 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5376 | 管理器设置完成，包含增强版表头管理器
2025-08-19 09:58:54.336 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5381 | 🔧 开始应用窗口级Material Design样式...
2025-08-19 09:58:54.336 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-19 09:58:54.336 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-19 09:58:54.336 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5388 | ✅ 窗口级样式应用成功
2025-08-19 09:58:54.336 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5429 | ✅ 响应式样式监听设置完成
2025-08-19 09:58:54.336 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-19 09:58:54.336 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-19 09:58:54.336 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-19 09:58:54.336 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-19 09:58:54.336 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-19 09:58:54.353 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1911 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-19 09:58:54.367 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1825 | 使用兜底数据加载导航
2025-08-19 09:58:54.367 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-19 09:58:54.367 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-19 09:58:54.367 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 0个展开项
2025-08-19 09:58:54.367 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-19 09:58:54.372 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-19 09:58:54.373 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-19 09:58:54.375 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-19 09:58:54.379 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1911 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-19 09:58:54.384 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-19 09:58:54.385 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-19 09:58:54.386 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-19 09:58:54.386 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1395 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-19 09:58:54.387 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 09:58:54.389 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 09:58:54.389 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1592 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-19 09:58:54.390 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1400 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-19 09:58:54.627 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-19 09:58:54.627 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-19 09:58:54.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-19 09:58:54.637 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-19 09:58:54.660 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-19 09:58:54.660 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-19 09:58:54.661 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-19 09:58:54.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2197 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-19 09:58:54.664 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-19 09:58:54.664 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-19 09:58:54.665 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 09:58:54.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2249 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-19 09:58:54.675 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-19 09:58:54.677 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-19 09:58:54.678 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2296 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-19 09:58:54.678 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-19 09:58:54.678 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-19 09:58:54.680 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: False
2025-08-19 09:58:54.681 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-19 09:58:54.682 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-19 09:58:54.682 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2303 | 列宽管理器初始化完成
2025-08-19 09:58:54.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2430 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-19 09:58:54.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2317 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-19 09:58:54.686 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 09:58:54.692 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 09:58:54.692 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 09:58:54.700 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-19 09:58:54.701 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7594 | 方案A：安全设置列数: 0
2025-08-19 09:58:54.704 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2840 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-19 09:58:54.704 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 09:58:54.713 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-19 09:58:54.713 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2892 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-19 09:58:54.718 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 09:58:54.733 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 09:58:54.733 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 09:58:54.734 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 09:58:54.734 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 09:58:54.735 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 09:58:54.742 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-19 09:58:54.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 49.8ms
2025-08-19 09:58:54.747 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 09:58:54.748 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 09:58:54.749 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-19 09:58:54.750 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 09:58:54.751 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 09:58:54.760 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-19 09:58:54.776 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-19 09:58:54.777 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-19 09:58:54.820 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-19 09:58:54.865 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 09:58:54.865 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-19 09:58:54.866 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5338 | 快捷键设置完成
2025-08-19 09:58:54.866 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5295 | 主窗口UI设置完成。
2025-08-19 09:58:54.869 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5532 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-19 09:58:54.869 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5564 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-19 09:58:54.870 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5576 | ✅ 已连接分页刷新信号到主窗口
2025-08-19 09:58:54.871 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5577 | ✅ 已连接分页组件事件到新架构
2025-08-19 09:58:54.872 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5588 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-19 09:58:54.873 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5591 | 信号连接设置完成
2025-08-19 09:58:54.874 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6762 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-19 09:58:54.875 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-19 09:58:54.876 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-19 09:58:54.876 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6772 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-19 09:58:54.885 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 09:58:54.886 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 09:58:54.892 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 09:58:54.893 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 09:58:54.893 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 09:58:54.895 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7594 | 方案A：安全设置列数: 22
2025-08-19 09:58:54.899 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 09:58:54.905 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 09:58:54.909 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 09:58:54.911 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 09:58:54.912 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 19.4ms
2025-08-19 09:58:54.912 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 09:58:54.913 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 09:58:54.914 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-19 09:58:54.916 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 09:58:54.916 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 09:58:54.917 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 09:58:54.918 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8333 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-19 09:58:54.927 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 09:58:54.927 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 09:58:54.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 09:58:54.929 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 09:58:54.930 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 09:58:54.932 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 09:58:54.933 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 09:58:54.934 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 09:58:54.935 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 09:58:54.939 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 9.8ms
2025-08-19 09:58:54.939 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 09:58:54.940 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 09:58:54.940 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-19 09:58:54.941 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 09:58:54.943 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 09:58:54.944 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8351 | 已显示标准空表格，表头数量: 22
2025-08-19 09:58:54.944 | INFO     | src.gui.prototype.prototype_main_window:__init__:3644 | 原型主窗口初始化完成
2025-08-19 09:58:55.070 | INFO     | __main__:main:518 | 应用程序启动成功
2025-08-19 09:58:55.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 09:58:55.080 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 09:58:55.080 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 09:58:55.226 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-19 09:58:55.227 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2072 | MainWorkspaceArea 响应式适配: sm
2025-08-19 09:58:55.233 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1465 | 执行延迟的自动选择最新数据...
2025-08-19 09:58:55.234 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 09:58:55.235 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 09:58:55.236 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1400 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-19 09:58:55.236 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1488 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-19 09:58:55.390 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 09:58:55.391 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 09:58:55.393 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1400 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-19 09:58:55.734 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9235 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-19 09:58:55.735 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9145 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-19 09:58:55.738 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9159 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-19 09:58:55.738 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9693 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-19 09:58:55.755 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9165 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-19 09:58:56.237 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 09:58:56.238 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 09:58:56.239 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1603 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-19 09:58:56.240 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1400 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-19 09:59:05.797 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-19 09:59:05.798 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7205 | 导航变化: 工资表 > 2025年 > 05月 > A岗职工
2025-08-19 09:59:05.801 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10324 | 🚫 [用户要求] 表格切换不显示加载条:  -> None
2025-08-19 09:59:05.802 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10340 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 09:59:05.803 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8132 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', 'A岗职工'] -> salary_data_2025_05_a_grade_employees
2025-08-19 09:59:05.803 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 09:59:05.804 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_a_grade_employees 的缓存
2025-08-19 09:59:05.806 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11010 | 已注册 2 个表格到表头管理器
2025-08-19 09:59:05.806 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-19 09:59:05.807 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 1.56ms
2025-08-19 09:59:05.807 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-19 09:59:05.809 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7421 | 🆕 使用新架构加载数据: salary_data_2025_05_a_grade_employees（通过事件系统）
2025-08-19 09:59:05.821 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-19 09:59:05.822 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 09:59:05.822 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 09:59:05.824 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7430 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_a_grade_employees 尚未创建，显示空表格等待数据导入
2025-08-19 09:59:05.825 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8279 | 🔧 [数据流追踪] 使用A岗职工表头: 20个字段
2025-08-19 09:59:05.826 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8329 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_a_grade_employees 的专用表头: 20个字段
2025-08-19 09:59:05.830 | INFO     | src.gui.prototype.prototype_main_window:set_data:845 | 空数据输入发生 3 次（2s 窗口），将显示空表提示
2025-08-19 09:59:05.835 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 09:59:05.835 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 09:59:05.836 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 09:59:05.837 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 09:59:05.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 09:59:05.838 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 09:59:05.840 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 09:59:05.840 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 09:59:05.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 09:59:05.842 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 09:59:05.843 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 5.6ms
2025-08-19 09:59:05.844 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 09:59:05.853 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 09:59:05.854 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 09:59:05.855 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 09:59:05.856 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 09:59:05.856 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 09:59:05.857 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8351 | 已显示标准空表格，表头数量: 20
2025-08-19 09:59:05.955 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 09:59:06.507 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 退休人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-08-19 09:59:06.508 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7205 | 导航变化: 工资表 > 2025年 > 05月 > 退休人员
2025-08-19 09:59:06.511 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10324 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_a_grade_employees -> None
2025-08-19 09:59:06.512 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10340 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 09:59:06.513 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8132 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '退休人员'] -> salary_data_2025_05_pension_employees
2025-08-19 09:59:06.514 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 09:59:06.515 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_pension_employees 的缓存
2025-08-19 09:59:06.516 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11010 | 已注册 2 个表格到表头管理器
2025-08-19 09:59:06.516 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7421 | 🆕 使用新架构加载数据: salary_data_2025_05_pension_employees（通过事件系统）
2025-08-19 09:59:06.518 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 09:59:06.523 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 09:59:06.524 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7430 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_pension_employees 尚未创建，显示空表格等待数据导入
2025-08-19 09:59:06.524 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8268 | 🔧 [数据流追踪] 使用退休人员表头: 26个字段
2025-08-19 09:59:06.525 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8329 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_pension_employees 的专用表头: 26个字段
2025-08-19 09:59:06.526 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 09:59:06.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 09:59:06.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 09:59:06.528 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 09:59:06.529 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 09:59:06.530 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 09:59:06.530 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 09:59:06.531 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 09:59:06.536 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 09:59:06.537 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 8.7ms
2025-08-19 09:59:06.537 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 09:59:06.538 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 09:59:06.540 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 09:59:06.540 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 09:59:06.541 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 09:59:06.545 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8351 | 已显示标准空表格，表头数量: 26
2025-08-19 09:59:06.640 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 09:59:07.396 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 离休人员', '工资表', '工资表 > 2025年']
2025-08-19 09:59:07.396 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7205 | 导航变化: 工资表 > 2025年 > 05月 > 离休人员
2025-08-19 09:59:07.401 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10324 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_pension_employees -> None
2025-08-19 09:59:07.402 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10340 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 09:59:07.403 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8132 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '离休人员'] -> salary_data_2025_05_retired_employees
2025-08-19 09:59:07.403 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 09:59:07.404 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_retired_employees 的缓存
2025-08-19 09:59:07.405 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11010 | 已注册 2 个表格到表头管理器
2025-08-19 09:59:07.406 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-19 09:59:07.407 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 1.11ms
2025-08-19 09:59:07.407 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-19 09:59:07.408 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7421 | 🆕 使用新架构加载数据: salary_data_2025_05_retired_employees（通过事件系统）
2025-08-19 09:59:07.413 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-19 09:59:07.414 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 09:59:07.415 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 09:59:07.415 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7430 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_retired_employees 尚未创建，显示空表格等待数据导入
2025-08-19 09:59:07.416 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8256 | 🔧 [数据流追踪] 使用离休人员表头: 15个字段
2025-08-19 09:59:07.417 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8329 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_retired_employees 的专用表头: 15个字段
2025-08-19 09:59:07.419 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 09:59:07.419 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 09:59:07.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 09:59:07.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 09:59:07.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 09:59:07.425 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 09:59:07.425 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 09:59:07.431 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 09:59:07.432 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 09:59:07.432 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 09:59:07.433 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 13.7ms
2025-08-19 09:59:07.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 09:59:07.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 09:59:07.437 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 09:59:07.437 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 09:59:07.438 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 09:59:07.439 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 09:59:07.444 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8351 | 已显示标准空表格，表头数量: 15
2025-08-19 09:59:07.537 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 09:59:09.132 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2072 | MainWorkspaceArea 响应式适配: sm
2025-08-19 09:59:09.137 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1395 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-19 09:59:09.137 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 09:59:09.138 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 09:59:09.138 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1592 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-19 09:59:09.139 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1400 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-19 09:59:10.076 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表']
2025-08-19 09:59:10.077 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7205 | 导航变化: 异动人员表
2025-08-19 09:59:10.080 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10324 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_retired_employees -> None
2025-08-19 09:59:10.081 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10340 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 09:59:10.081 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8005 | 🔧 [表名生成] 处理异动表路径: ['异动人员表']
2025-08-19 09:59:10.082 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8051 | 🔧 [P1修复] 异动表路径层次较少，使用当前年月: change_data_2025_08_active_employees
2025-08-19 09:59:10.082 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8053 | 🔧 [表名生成] 生成异动表名: change_data_2025_08_active_employees
2025-08-19 09:59:10.083 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 09:59:10.084 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 change_data_2025_08_active_employees 的缓存
2025-08-19 09:59:10.086 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11010 | 已注册 2 个表格到表头管理器
2025-08-19 09:59:10.087 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-19 09:59:10.088 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 1.02ms
2025-08-19 09:59:10.096 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-19 09:59:10.097 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7421 | 🆕 使用新架构加载数据: change_data_2025_08_active_employees（通过事件系统）
2025-08-19 09:59:10.098 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-19 09:59:10.099 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 09:59:10.101 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 09:59:10.101 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7430 | 🔧 [数据流追踪] 数据表 change_data_2025_08_active_employees 尚未创建，显示空表格等待数据导入
2025-08-19 09:59:10.102 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8246 | 🔧 [数据流追踪] 使用在职人员表头: 22个字段
2025-08-19 09:59:10.103 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8329 | 🔧 [数据流追踪] 使用表 change_data_2025_08_active_employees 的专用表头: 22个字段
2025-08-19 09:59:10.104 | INFO     | src.gui.prototype.prototype_main_window:set_data:845 | 空数据输入发生 3 次（2s 窗口），将显示空表提示
2025-08-19 09:59:10.108 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 09:59:10.109 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 09:59:10.109 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 09:59:10.110 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 09:59:10.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 09:59:10.112 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 09:59:10.112 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 09:59:10.114 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 09:59:10.115 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 09:59:10.115 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 09:59:10.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 5.7ms
2025-08-19 09:59:10.117 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 09:59:10.125 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 09:59:10.126 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 09:59:10.127 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 09:59:10.127 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 09:59:10.128 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 09:59:10.129 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8351 | 已显示标准空表格，表头数量: 22
2025-08-19 09:59:10.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 09:59:11.431 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-19 09:59:11.431 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5824 | 接收到数据导入请求，推断的目标路径: 异动人员表。打开导入对话框。
2025-08-19 09:59:11.431 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-19 09:59:11.431 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:75 | 多Sheet导入器初始化完成
2025-08-19 09:59:11.431 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-08-19 09:59:11.478 | CRITICAL | __main__:qt_aware_excepthook:167 | 🔧 [P0-CRITICAL] PyQt异常捕获: AttributeError: 'QComboBox' object has no attribute 'currentDataChanged'
2025-08-19 09:59:11.478 | CRITICAL | __main__:qt_aware_excepthook:168 | 🔧 [P0-CRITICAL] 异常堆栈:
Traceback (most recent call last):
  File "C:\test\salary_changes\salary_changes\src\gui\prototype\prototype_main_window.py", line 5826, in _on_import_data_requested
    dialog = DataImportDialog(
             ^^^^^^^^^^^^^^^^^
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 73, in __init__
    self._init_ui()
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 122, in _init_ui
    self._create_import_tabs(scroll_layout)
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 139, in _create_import_tabs
    self._create_excel_import_tab()
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 219, in _create_excel_import_tab
    self._create_change_data_template_options(options_layout)
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 385, in _create_change_data_template_options
    self.change_mode_combo.currentDataChanged.connect(self._on_change_mode_changed)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'QComboBox' object has no attribute 'currentDataChanged'. Did you mean: 'currentTextChanged'?

2025-08-19 09:59:11.478 | ERROR    | __main__:qt_aware_excepthook:223 | 🔧 [P0-CRITICAL] 错误对话框设置失败: arguments did not match any overloaded call:
  invokeMethod(obj: QObject, member: str, type: Qt.ConnectionType, ret: QGenericReturnArgument, value0: QGenericArgument = QGenericArgument(0,0), value1: QGenericArgument = QGenericArgument(0,0), value2: QGenericArgument = QGenericArgument(0,0), value3: QGenericArgument = QGenericArgument(0,0), value4: QGenericArgument = QGenericArgument(0,0), value5: QGenericArgument = QGenericArgument(0,0), value6: QGenericArgument = QGenericArgument(0,0), value7: QGenericArgument = QGenericArgument(0,0), value8: QGenericArgument = QGenericArgument(0,0), value9: QGenericArgument = QGenericArgument(0,0)): argument 2 has unexpected type 'function'
  invokeMethod(obj: QObject, member: str, ret: QGenericReturnArgument, value0: QGenericArgument = QGenericArgument(0,0), value1: QGenericArgument = QGenericArgument(0,0), value2: QGenericArgument = QGenericArgument(0,0), value3: QGenericArgument = QGenericArgument(0,0), value4: QGenericArgument = QGenericArgument(0,0), value5: QGenericArgument = QGenericArgument(0,0), value6: QGenericArgument = QGenericArgument(0,0), value7: QGenericArgument = QGenericArgument(0,0), value8: QGenericArgument = QGenericArgument(0,0), value9: QGenericArgument = QGenericArgument(0,0)): argument 2 has unexpected type 'function'
  invokeMethod(obj: QObject, member: str, type: Qt.ConnectionType, value0: QGenericArgument = QGenericArgument(0,0), value1: QGenericArgument = QGenericArgument(0,0), value2: QGenericArgument = QGenericArgument(0,0), value3: QGenericArgument = QGenericArgument(0,0), value4: QGenericArgument = QGenericArgument(0,0), value5: QGenericArgument = QGenericArgument(0,0), value6: QGenericArgument = QGenericArgument(0,0), value7: QGenericArgument = QGenericArgument(0,0), value8: QGenericArgument = QGenericArgument(0,0), value9: QGenericArgument = QGenericArgument(0,0)): argument 2 has unexpected type 'function'
  invokeMethod(obj: QObject, member: str, value0: QGenericArgument = QGenericArgument(0,0), value1: QGenericArgument = QGenericArgument(0,0), value2: QGenericArgument = QGenericArgument(0,0), value3: QGenericArgument = QGenericArgument(0,0), value4: QGenericArgument = QGenericArgument(0,0), value5: QGenericArgument = QGenericArgument(0,0), value6: QGenericArgument = QGenericArgument(0,0), value7: QGenericArgument = QGenericArgument(0,0), value8: QGenericArgument = QGenericArgument(0,0), value9: QGenericArgument = QGenericArgument(0,0)): argument 2 has unexpected type 'function'
2025-08-19 09:59:11.478 | INFO     | __main__:qt_aware_excepthook:240 | 🔧 [P0-CRITICAL] 异常已处理，程序继续运行
2025-08-19 09:59:16.790 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-19 09:59:16.790 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5824 | 接收到数据导入请求，推断的目标路径: 异动人员表。打开导入对话框。
2025-08-19 09:59:16.790 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-19 09:59:16.806 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:75 | 多Sheet导入器初始化完成
2025-08-19 09:59:16.806 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-08-19 09:59:16.822 | CRITICAL | __main__:qt_aware_excepthook:167 | 🔧 [P0-CRITICAL] PyQt异常捕获: AttributeError: 'QComboBox' object has no attribute 'currentDataChanged'
2025-08-19 09:59:16.822 | CRITICAL | __main__:qt_aware_excepthook:168 | 🔧 [P0-CRITICAL] 异常堆栈:
Traceback (most recent call last):
  File "C:\test\salary_changes\salary_changes\src\gui\prototype\prototype_main_window.py", line 5826, in _on_import_data_requested
    dialog = DataImportDialog(
             ^^^^^^^^^^^^^^^^^
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 73, in __init__
    self._init_ui()
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 122, in _init_ui
    self._create_import_tabs(scroll_layout)
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 139, in _create_import_tabs
    self._create_excel_import_tab()
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 219, in _create_excel_import_tab
    self._create_change_data_template_options(options_layout)
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 385, in _create_change_data_template_options
    self.change_mode_combo.currentDataChanged.connect(self._on_change_mode_changed)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'QComboBox' object has no attribute 'currentDataChanged'. Did you mean: 'currentTextChanged'?

2025-08-19 09:59:16.822 | ERROR    | __main__:qt_aware_excepthook:223 | 🔧 [P0-CRITICAL] 错误对话框设置失败: arguments did not match any overloaded call:
  invokeMethod(obj: QObject, member: str, type: Qt.ConnectionType, ret: QGenericReturnArgument, value0: QGenericArgument = QGenericArgument(0,0), value1: QGenericArgument = QGenericArgument(0,0), value2: QGenericArgument = QGenericArgument(0,0), value3: QGenericArgument = QGenericArgument(0,0), value4: QGenericArgument = QGenericArgument(0,0), value5: QGenericArgument = QGenericArgument(0,0), value6: QGenericArgument = QGenericArgument(0,0), value7: QGenericArgument = QGenericArgument(0,0), value8: QGenericArgument = QGenericArgument(0,0), value9: QGenericArgument = QGenericArgument(0,0)): argument 2 has unexpected type 'function'
  invokeMethod(obj: QObject, member: str, ret: QGenericReturnArgument, value0: QGenericArgument = QGenericArgument(0,0), value1: QGenericArgument = QGenericArgument(0,0), value2: QGenericArgument = QGenericArgument(0,0), value3: QGenericArgument = QGenericArgument(0,0), value4: QGenericArgument = QGenericArgument(0,0), value5: QGenericArgument = QGenericArgument(0,0), value6: QGenericArgument = QGenericArgument(0,0), value7: QGenericArgument = QGenericArgument(0,0), value8: QGenericArgument = QGenericArgument(0,0), value9: QGenericArgument = QGenericArgument(0,0)): argument 2 has unexpected type 'function'
  invokeMethod(obj: QObject, member: str, type: Qt.ConnectionType, value0: QGenericArgument = QGenericArgument(0,0), value1: QGenericArgument = QGenericArgument(0,0), value2: QGenericArgument = QGenericArgument(0,0), value3: QGenericArgument = QGenericArgument(0,0), value4: QGenericArgument = QGenericArgument(0,0), value5: QGenericArgument = QGenericArgument(0,0), value6: QGenericArgument = QGenericArgument(0,0), value7: QGenericArgument = QGenericArgument(0,0), value8: QGenericArgument = QGenericArgument(0,0), value9: QGenericArgument = QGenericArgument(0,0)): argument 2 has unexpected type 'function'
  invokeMethod(obj: QObject, member: str, value0: QGenericArgument = QGenericArgument(0,0), value1: QGenericArgument = QGenericArgument(0,0), value2: QGenericArgument = QGenericArgument(0,0), value3: QGenericArgument = QGenericArgument(0,0), value4: QGenericArgument = QGenericArgument(0,0), value5: QGenericArgument = QGenericArgument(0,0), value6: QGenericArgument = QGenericArgument(0,0), value7: QGenericArgument = QGenericArgument(0,0), value8: QGenericArgument = QGenericArgument(0,0), value9: QGenericArgument = QGenericArgument(0,0)): argument 2 has unexpected type 'function'
2025-08-19 09:59:16.822 | INFO     | __main__:qt_aware_excepthook:240 | 🔧 [P0-CRITICAL] 异常已处理，程序继续运行
2025-08-19 09:59:25.181 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2072 | MainWorkspaceArea 响应式适配: sm
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年']
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7205 | 导航变化: 工资表
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10324 | 🚫 [用户要求] 表格切换不显示加载条: change_data_2025_08_active_employees -> None
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10340 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8063 | 🔧 [表名生成] 导航路径不完整(1层): ['工资表']
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 09:59:26.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 09:59:26.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 15.1ms
2025-08-19 09:59:26.653 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 09:59:26.654 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 09:59:26.655 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 09:59:26.656 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 09:59:26.657 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 09:59:26.657 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 09:59:26.658 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8246 | 🔧 [数据流追踪] 使用在职人员表头: 22个字段
2025-08-19 09:59:26.659 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8329 | 🔧 [数据流追踪] 使用表 change_data_2025_08_active_employees 的专用表头: 22个字段
2025-08-19 09:59:26.660 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 09:59:26.660 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 09:59:26.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 09:59:26.662 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 09:59:26.669 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 09:59:26.671 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 09:59:26.672 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 09:59:26.673 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 09:59:26.673 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 09:59:26.674 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 12.8ms
2025-08-19 09:59:26.675 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 09:59:26.676 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 09:59:26.677 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 09:59:26.677 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 09:59:26.678 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 09:59:26.683 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8351 | 已显示标准空表格，表头数量: 22
2025-08-19 09:59:26.684 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:1025 | 导航选择: 工资表
2025-08-19 09:59:26.757 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 09:59:26.778 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 09:59:27.794 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-19 09:59:27.794 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5824 | 接收到数据导入请求，推断的目标路径: 工资表。打开导入对话框。
2025-08-19 09:59:27.794 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-19 09:59:27.794 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:75 | 多Sheet导入器初始化完成
2025-08-19 09:59:27.794 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-08-19 09:59:27.825 | CRITICAL | __main__:qt_aware_excepthook:167 | 🔧 [P0-CRITICAL] PyQt异常捕获: AttributeError: 'QComboBox' object has no attribute 'currentDataChanged'
2025-08-19 09:59:27.825 | CRITICAL | __main__:qt_aware_excepthook:168 | 🔧 [P0-CRITICAL] 异常堆栈:
Traceback (most recent call last):
  File "C:\test\salary_changes\salary_changes\src\gui\prototype\prototype_main_window.py", line 5826, in _on_import_data_requested
    dialog = DataImportDialog(
             ^^^^^^^^^^^^^^^^^
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 73, in __init__
    self._init_ui()
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 122, in _init_ui
    self._create_import_tabs(scroll_layout)
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 139, in _create_import_tabs
    self._create_excel_import_tab()
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 219, in _create_excel_import_tab
    self._create_change_data_template_options(options_layout)
  File "C:\test\salary_changes\salary_changes\src\gui\main_dialogs.py", line 385, in _create_change_data_template_options
    self.change_mode_combo.currentDataChanged.connect(self._on_change_mode_changed)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'QComboBox' object has no attribute 'currentDataChanged'. Did you mean: 'currentTextChanged'?

2025-08-19 09:59:27.825 | ERROR    | __main__:qt_aware_excepthook:223 | 🔧 [P0-CRITICAL] 错误对话框设置失败: arguments did not match any overloaded call:
  invokeMethod(obj: QObject, member: str, type: Qt.ConnectionType, ret: QGenericReturnArgument, value0: QGenericArgument = QGenericArgument(0,0), value1: QGenericArgument = QGenericArgument(0,0), value2: QGenericArgument = QGenericArgument(0,0), value3: QGenericArgument = QGenericArgument(0,0), value4: QGenericArgument = QGenericArgument(0,0), value5: QGenericArgument = QGenericArgument(0,0), value6: QGenericArgument = QGenericArgument(0,0), value7: QGenericArgument = QGenericArgument(0,0), value8: QGenericArgument = QGenericArgument(0,0), value9: QGenericArgument = QGenericArgument(0,0)): argument 2 has unexpected type 'function'
  invokeMethod(obj: QObject, member: str, ret: QGenericReturnArgument, value0: QGenericArgument = QGenericArgument(0,0), value1: QGenericArgument = QGenericArgument(0,0), value2: QGenericArgument = QGenericArgument(0,0), value3: QGenericArgument = QGenericArgument(0,0), value4: QGenericArgument = QGenericArgument(0,0), value5: QGenericArgument = QGenericArgument(0,0), value6: QGenericArgument = QGenericArgument(0,0), value7: QGenericArgument = QGenericArgument(0,0), value8: QGenericArgument = QGenericArgument(0,0), value9: QGenericArgument = QGenericArgument(0,0)): argument 2 has unexpected type 'function'
  invokeMethod(obj: QObject, member: str, type: Qt.ConnectionType, value0: QGenericArgument = QGenericArgument(0,0), value1: QGenericArgument = QGenericArgument(0,0), value2: QGenericArgument = QGenericArgument(0,0), value3: QGenericArgument = QGenericArgument(0,0), value4: QGenericArgument = QGenericArgument(0,0), value5: QGenericArgument = QGenericArgument(0,0), value6: QGenericArgument = QGenericArgument(0,0), value7: QGenericArgument = QGenericArgument(0,0), value8: QGenericArgument = QGenericArgument(0,0), value9: QGenericArgument = QGenericArgument(0,0)): argument 2 has unexpected type 'function'
  invokeMethod(obj: QObject, member: str, value0: QGenericArgument = QGenericArgument(0,0), value1: QGenericArgument = QGenericArgument(0,0), value2: QGenericArgument = QGenericArgument(0,0), value3: QGenericArgument = QGenericArgument(0,0), value4: QGenericArgument = QGenericArgument(0,0), value5: QGenericArgument = QGenericArgument(0,0), value6: QGenericArgument = QGenericArgument(0,0), value7: QGenericArgument = QGenericArgument(0,0), value8: QGenericArgument = QGenericArgument(0,0), value9: QGenericArgument = QGenericArgument(0,0)): argument 2 has unexpected type 'function'
2025-08-19 09:59:27.825 | INFO     | __main__:qt_aware_excepthook:240 | 🔧 [P0-CRITICAL] 异常已处理，程序继续运行
2025-08-19 09:59:30.639 | INFO     | __main__:main:523 | 应用程序正常退出
2025-08-19 09:59:30.639 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_0_1868484904000 已自动清理（弱引用回调）
