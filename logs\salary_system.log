2025-08-19 12:13:03.476 | INFO     | src.utils.log_config:_log_initialization_info:356 | 日志系统初始化完成
2025-08-19 12:13:03.476 | INFO     | src.utils.log_config:_log_initialization_info:357 | 日志级别: INFO
2025-08-19 12:13:03.476 | INFO     | src.utils.log_config:_log_initialization_info:358 | 控制台输出: True
2025-08-19 12:13:03.476 | INFO     | src.utils.log_config:_log_initialization_info:359 | 文件输出: True
2025-08-19 12:13:03.476 | INFO     | src.utils.log_config:_log_initialization_info:365 | 日志文件路径: logs/salary_system.log
2025-08-19 12:13:03.476 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-19 12:13:06.573 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-19 12:13:06.573 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-19 12:13:06.573 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-19 12:13:06.573 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-19 12:13:06.573 | INFO     | __main__:setup_app_logging:427 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-19 12:13:06.573 | INFO     | __main__:main:491 | 初始化核心管理器...
2025-08-19 12:13:06.573 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:13:06.573 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-19 12:13:06.573 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-19 12:13:06.573 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-19 12:13:06.573 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-19 12:13:06.651 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-19 12:13:06.651 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-19 12:13:06.651 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:13:06.667 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-19 12:13:06.667 | INFO     | __main__:main:496 | 核心管理器初始化完成。
2025-08-19 12:13:06.667 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-19 12:13:06.667 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-19 12:13:06.667 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:13:06.667 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-19 12:13:06.667 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-19 12:13:06.667 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-19 12:13:06.667 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-19 12:13:06.667 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11897 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-19 12:13:06.667 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:13:06.667 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11752 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-19 12:13:06.667 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11790 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-19 12:13:06.727 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-19 12:13:06.728 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-19 12:13:06.735 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:13:06.741 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:149 | 🔧 [配置修复] 创建了完整的默认配置，包含基本字段映射和模板
2025-08-19 12:13:06.742 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-19 12:13:06.742 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-19 12:13:06.748 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:13:06.748 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-19 12:13:06.748 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-19 12:13:06.748 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-19 12:13:06.749 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:13:06.749 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-19 12:13:06.749 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 14.5ms
2025-08-19 12:13:06.767 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-19 12:13:06.767 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-19 12:13:06.767 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-19 12:13:06.767 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-19 12:13:06.768 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-19 12:13:06.768 | INFO     | src.gui.prototype.prototype_main_window:__init__:3590 | 🚀 性能管理器已集成
2025-08-19 12:13:06.768 | INFO     | src.gui.prototype.prototype_main_window:__init__:3592 | ✅ 新架构集成成功！
2025-08-19 12:13:06.769 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3705 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-19 12:13:06.769 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3670 | ✅ 新架构事件监听器设置完成
2025-08-19 12:13:06.776 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-19 12:13:06.777 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-19 12:13:06.777 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-19 12:13:07.007 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2666 | 菜单栏创建完成
2025-08-19 12:13:07.007 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-19 12:13:07.007 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-19 12:13:07.007 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-19 12:13:07.007 | INFO     | src.gui.prototype.prototype_main_window:__init__:2641 | 菜单栏管理器初始化完成
2025-08-19 12:13:07.007 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-19 12:13:07.007 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5376 | 管理器设置完成，包含增强版表头管理器
2025-08-19 12:13:07.007 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5381 | 🔧 开始应用窗口级Material Design样式...
2025-08-19 12:13:07.007 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-19 12:13:07.007 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-19 12:13:07.007 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5388 | ✅ 窗口级样式应用成功
2025-08-19 12:13:07.007 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5429 | ✅ 响应式样式监听设置完成
2025-08-19 12:13:07.022 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-19 12:13:07.022 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-19 12:13:07.022 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-19 12:13:07.022 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-19 12:13:07.038 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-19 12:13:07.038 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1911 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-19 12:13:07.038 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1870 | 使用兜底数据加载导航
2025-08-19 12:13:07.038 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-19 12:13:07.054 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-19 12:13:07.054 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 0个展开项
2025-08-19 12:13:07.054 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-19 12:13:07.065 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-19 12:13:07.065 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-19 12:13:07.067 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-19 12:13:07.071 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1911 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-19 12:13:07.077 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-19 12:13:07.077 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-19 12:13:07.078 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-19 12:13:07.078 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-优化] 开始自动选择最新数据...
2025-08-19 12:13:07.079 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 12:13:07.080 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 12:13:07.080 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1403 | 🔧 [P1-优化] 暂未找到最新工资数据，可能需要先导入数据
2025-08-19 12:13:07.326 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-19 12:13:07.326 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-19 12:13:07.328 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-19 12:13:07.330 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-19 12:13:07.351 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-19 12:13:07.351 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-19 12:13:07.352 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-19 12:13:07.352 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2197 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-19 12:13:07.355 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-19 12:13:07.355 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-19 12:13:07.356 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:13:07.356 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2249 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-19 12:13:07.366 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-19 12:13:07.371 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-19 12:13:07.371 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2296 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-19 12:13:07.372 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-19 12:13:07.372 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-19 12:13:07.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: False
2025-08-19 12:13:07.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-19 12:13:07.375 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-19 12:13:07.376 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2303 | 列宽管理器初始化完成
2025-08-19 12:13:07.376 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2430 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-19 12:13:07.379 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2317 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-19 12:13:07.379 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:13:07.384 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:13:07.385 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:13:07.392 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-19 12:13:07.393 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7594 | 方案A：安全设置列数: 0
2025-08-19 12:13:07.401 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2840 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-19 12:13:07.401 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:13:07.409 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-19 12:13:07.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2892 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-19 12:13:07.413 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:13:07.427 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:13:07.428 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:13:07.429 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:13:07.430 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:13:07.430 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 12:13:07.434 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-19 12:13:07.436 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 49.0ms
2025-08-19 12:13:07.437 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:13:07.444 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:13:07.444 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-19 12:13:07.445 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:13:07.446 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:13:07.456 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-19 12:13:07.467 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-19 12:13:07.468 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-19 12:13:07.510 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-19 12:13:07.558 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:13:07.558 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-19 12:13:07.559 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5338 | 快捷键设置完成
2025-08-19 12:13:07.559 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5295 | 主窗口UI设置完成。
2025-08-19 12:13:07.562 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5532 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-19 12:13:07.563 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5564 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-19 12:13:07.564 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5576 | ✅ 已连接分页刷新信号到主窗口
2025-08-19 12:13:07.564 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5577 | ✅ 已连接分页组件事件到新架构
2025-08-19 12:13:07.566 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5588 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-19 12:13:07.567 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5591 | 信号连接设置完成
2025-08-19 12:13:07.568 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6849 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-19 12:13:07.569 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-19 12:13:07.569 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-19 12:13:07.576 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6859 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-19 12:13:07.588 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:13:07.589 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:13:07.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:13:07.591 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:13:07.593 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:13:07.594 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7594 | 方案A：安全设置列数: 22
2025-08-19 12:13:07.595 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:13:07.599 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:13:07.600 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:13:07.601 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:13:07.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 10.6ms
2025-08-19 12:13:07.603 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:13:07.603 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:13:07.604 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-19 12:13:07.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:13:07.608 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:13:07.614 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:13:07.615 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8628 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-19 12:13:07.615 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:13:07.616 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:13:07.616 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:13:07.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:13:07.619 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:13:07.619 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:13:07.620 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:13:07.621 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:13:07.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:13:07.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 10.9ms
2025-08-19 12:13:07.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:13:07.629 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:13:07.630 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-19 12:13:07.631 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:13:07.632 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:13:07.633 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8649 | 已显示标准空表格，表头数量: 22
2025-08-19 12:13:07.633 | INFO     | src.gui.prototype.prototype_main_window:__init__:3644 | 原型主窗口初始化完成
2025-08-19 12:13:07.777 | INFO     | __main__:main:518 | 应用程序启动成功
2025-08-19 12:13:07.785 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:13:07.787 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:13:07.791 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:13:07.861 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1468 | 执行延迟的自动选择最新数据...
2025-08-19 12:13:07.863 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-19 12:13:07.867 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2072 | MainWorkspaceArea 响应式适配: sm
2025-08-19 12:13:07.928 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 12:13:07.930 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 12:13:07.934 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1491 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-19 12:13:08.374 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9533 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-19 12:13:08.375 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9443 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-19 12:13:08.379 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9457 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-19 12:13:08.379 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9991 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-19 12:13:08.396 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9463 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-19 12:13:16.366 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-19 12:13:16.366 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7292 | 导航变化: 工资表 > 2025年 > 05月 > A岗职工
2025-08-19 12:13:16.366 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10622 | 🚫 [用户要求] 表格切换不显示加载条:  -> None
2025-08-19 12:13:16.366 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10638 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 12:13:16.366 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8219 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', 'A岗职工'] -> salary_data_2025_05_a_grade_employees
2025-08-19 12:13:16.366 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:13:16.382 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_a_grade_employees 的缓存
2025-08-19 12:13:16.382 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11308 | 已注册 2 个表格到表头管理器
2025-08-19 12:13:16.382 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-19 12:13:16.382 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-08-19 12:13:16.382 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-19 12:13:16.382 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7508 | 🆕 使用新架构加载数据: salary_data_2025_05_a_grade_employees（通过事件系统）
2025-08-19 12:13:16.382 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-19 12:13:16.382 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 12:13:16.382 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 12:13:16.382 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7517 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_a_grade_employees 尚未创建，显示空表格等待数据导入
2025-08-19 12:13:16.382 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8527 | 🔧 [P2-优化] 使用统一表头: 22个字段，表类型: a_grade_employees
2025-08-19 12:13:16.382 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8624 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_a_grade_employees 的专用表头: 22个字段
2025-08-19 12:13:16.382 | INFO     | src.gui.prototype.prototype_main_window:set_data:845 | 空数据输入发生 3 次（2s 窗口），将显示空表提示
2025-08-19 12:13:16.382 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:13:16.382 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:13:16.382 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:13:16.382 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:13:16.397 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:13:16.397 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:13:16.397 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:13:16.397 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:13:16.414 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:13:16.414 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 12:13:16.416 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 34.6ms
2025-08-19 12:13:16.416 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:13:16.417 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:13:16.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:13:16.419 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:13:16.419 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:13:16.421 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:13:16.425 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8649 | 已显示标准空表格，表头数量: 22
2025-08-19 12:13:16.519 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:13:17.222 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 退休人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-08-19 12:13:17.222 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7292 | 导航变化: 工资表 > 2025年 > 05月 > 退休人员
2025-08-19 12:13:17.222 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10622 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_a_grade_employees -> None
2025-08-19 12:13:17.222 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10638 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 12:13:17.222 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8219 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '退休人员'] -> salary_data_2025_05_pension_employees
2025-08-19 12:13:17.222 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:13:17.222 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_pension_employees 的缓存
2025-08-19 12:13:17.222 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11308 | 已注册 2 个表格到表头管理器
2025-08-19 12:13:17.238 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7508 | 🆕 使用新架构加载数据: salary_data_2025_05_pension_employees（通过事件系统）
2025-08-19 12:13:17.238 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 12:13:17.238 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 12:13:17.238 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7517 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_pension_employees 尚未创建，显示空表格等待数据导入
2025-08-19 12:13:17.238 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8527 | 🔧 [P2-优化] 使用统一表头: 22个字段，表类型: pension_employees
2025-08-19 12:13:17.238 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8624 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_pension_employees 的专用表头: 22个字段
2025-08-19 12:13:17.238 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:13:17.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:13:17.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:13:17.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:13:17.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:13:17.238 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:13:17.238 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:13:17.238 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:13:17.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:13:17.250 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 11.9ms
2025-08-19 12:13:17.260 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:13:17.260 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:13:17.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:13:17.263 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:13:17.263 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:13:17.264 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8649 | 已显示标准空表格，表头数量: 22
2025-08-19 12:13:17.364 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:13:18.255 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 离休人员', '工资表', '工资表 > 2025年']
2025-08-19 12:13:18.255 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7292 | 导航变化: 工资表 > 2025年 > 05月 > 离休人员
2025-08-19 12:13:18.270 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10622 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_pension_employees -> None
2025-08-19 12:13:18.270 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10638 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 12:13:18.270 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8219 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '离休人员'] -> salary_data_2025_05_retired_employees
2025-08-19 12:13:18.270 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:13:18.270 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_retired_employees 的缓存
2025-08-19 12:13:18.270 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11308 | 已注册 2 个表格到表头管理器
2025-08-19 12:13:18.270 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-19 12:13:18.270 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-08-19 12:13:18.270 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-19 12:13:18.270 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7508 | 🆕 使用新架构加载数据: salary_data_2025_05_retired_employees（通过事件系统）
2025-08-19 12:13:18.270 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-19 12:13:18.270 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 12:13:18.270 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 12:13:18.270 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7517 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_retired_employees 尚未创建，显示空表格等待数据导入
2025-08-19 12:13:18.270 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8527 | 🔧 [P2-优化] 使用统一表头: 22个字段，表类型: retired_employees
2025-08-19 12:13:18.270 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8624 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_retired_employees 的专用表头: 22个字段
2025-08-19 12:13:18.286 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:13:18.286 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:13:18.286 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:13:18.286 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:13:18.286 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:13:18.286 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:13:18.286 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:13:18.286 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:13:18.286 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:13:18.286 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 12:13:18.298 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 11.9ms
2025-08-19 12:13:18.298 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:13:18.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:13:18.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:13:18.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:13:18.309 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:13:18.310 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:13:18.311 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8649 | 已显示标准空表格，表头数量: 22
2025-08-19 12:13:18.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:13:18.737 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表']
2025-08-19 12:13:18.737 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7292 | 导航变化: 工资表 > 2025年 > 05月 > 全部在职人员
2025-08-19 12:13:18.737 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10622 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_retired_employees -> None
2025-08-19 12:13:18.737 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10638 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 12:13:18.737 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8219 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '全部在职人员'] -> salary_data_2025_05_active_employees
2025-08-19 12:13:18.737 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:13:18.737 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_active_employees 的缓存
2025-08-19 12:13:18.737 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11308 | 已注册 2 个表格到表头管理器
2025-08-19 12:13:18.737 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7508 | 🆕 使用新架构加载数据: salary_data_2025_05_active_employees（通过事件系统）
2025-08-19 12:13:18.737 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 12:13:18.737 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 12:13:18.737 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7517 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_active_employees 尚未创建，显示空表格等待数据导入
2025-08-19 12:13:18.737 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8527 | 🔧 [P2-优化] 使用统一表头: 22个字段，表类型: active_employees
2025-08-19 12:13:18.737 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8624 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_active_employees 的专用表头: 22个字段
2025-08-19 12:13:18.754 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:13:18.754 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:13:18.754 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:13:18.754 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:13:18.754 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:13:18.754 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:13:18.754 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:13:18.754 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:13:18.754 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:13:18.765 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 10.8ms
2025-08-19 12:13:18.765 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:13:18.766 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:13:18.767 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:13:18.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:13:18.776 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:13:18.777 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8649 | 已显示标准空表格，表头数量: 22
2025-08-19 12:13:18.876 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:13:20.123 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2072 | MainWorkspaceArea 响应式适配: sm
2025-08-19 12:13:20.130 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-优化] 开始自动选择最新数据...
2025-08-19 12:13:20.130 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 12:13:20.131 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 12:13:20.132 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1403 | 🔧 [P1-优化] 暂未找到最新工资数据，可能需要先导入数据
2025-08-19 12:13:21.131 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-19 12:13:21.131 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5825 | 🔧 [P0-修复] 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 05月 > 全部在职人员。打开导入对话框。
2025-08-19 12:13:21.135 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5834 | 🔧 [P0-修复] 开始创建数据导入对话框...
2025-08-19 12:13:21.135 | INFO     | src.gui.main_dialogs:__init__:55 | 🔧 [P0-修复] 开始初始化数据导入对话框...
2025-08-19 12:13:21.136 | INFO     | src.gui.main_dialogs:__init__:71 | 🔧 [P0-修复] 初始化导入器和验证器...
2025-08-19 12:13:21.137 | INFO     | src.gui.main_dialogs:__init__:76 | 🔧 [P0-修复] 初始化多Sheet导入器...
2025-08-19 12:13:21.137 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-19 12:13:21.138 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:75 | 多Sheet导入器初始化完成
2025-08-19 12:13:21.138 | INFO     | src.gui.main_dialogs:__init__:80 | 🔧 [P0-修复] 初始化默认设置管理器...
2025-08-19 12:13:21.139 | INFO     | src.gui.main_dialogs:__init__:85 | 🔧 [P0-修复] 开始初始化用户界面...
2025-08-19 12:13:21.143 | INFO     | src.gui.main_dialogs:_init_ui:110 | 🔧 [P0-修复] 设置对话框基本属性...
2025-08-19 12:13:21.149 | INFO     | src.gui.main_dialogs:_init_ui:120 | 🔧 [P0-修复] 创建主布局...
2025-08-19 12:13:21.150 | INFO     | src.gui.main_dialogs:_init_ui:125 | 🔧 [P0-修复] 创建目标选择组件...
2025-08-19 12:13:21.151 | INFO     | src.gui.widgets.target_selection_widget:__init__:112 | 🔧 [P0-修复] 开始初始化目标选择组件...
2025-08-19 12:13:21.151 | INFO     | src.gui.widgets.target_selection_widget:__init__:128 | 🔧 [P0-修复] 加载可选项配置...
2025-08-19 12:13:21.155 | INFO     | src.gui.widgets.target_selection_widget:_save_config:307 | 配置保存成功
2025-08-19 12:13:21.155 | INFO     | src.gui.widgets.target_selection_widget:__init__:132 | 🔧 [P0-修复] 初始化目标选择组件UI...
2025-08-19 12:13:21.168 | INFO     | src.gui.widgets.target_selection_widget:__init__:136 | 🔧 [P0-修复] 加载初始目标...
2025-08-19 12:13:21.171 | INFO     | src.gui.widgets.target_selection_widget:__init__:139 | 🔧 [P0-修复] 目标选择组件初始化完成
2025-08-19 12:13:21.171 | INFO     | src.gui.main_dialogs:_init_ui:130 | 🔧 [P0-修复] 设置目标路径: 工资表 > 2025年 > 05月 > 全部在职人员
2025-08-19 12:13:21.174 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:527 | 从路径设置目标: 工资表 > 2025年 > 05月 > 全部在职人员
2025-08-19 12:13:21.182 | ERROR    | src.gui.main_dialogs:__init__:101 | 🔧 [P0-修复] 数据导入对话框初始化失败: 'QComboBox' object has no attribute 'currentDataChanged'
2025-08-19 12:13:23.709 | ERROR    | src.gui.prototype.prototype_main_window:_on_import_data_requested:5875 | 🔧 [P0-修复] 打开数据导入对话框失败: 'QComboBox' object has no attribute 'currentDataChanged'
2025-08-19 12:13:26.128 | INFO     | __main__:main:523 | 应用程序正常退出
2025-08-19 12:13:26.134 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_0_3081676131648 已自动清理（弱引用回调）
2025-08-19 12:20:16.905 | INFO     | src.utils.log_config:_log_initialization_info:356 | 日志系统初始化完成
2025-08-19 12:20:16.906 | INFO     | src.utils.log_config:_log_initialization_info:357 | 日志级别: INFO
2025-08-19 12:20:16.906 | INFO     | src.utils.log_config:_log_initialization_info:358 | 控制台输出: True
2025-08-19 12:20:16.906 | INFO     | src.utils.log_config:_log_initialization_info:359 | 文件输出: True
2025-08-19 12:20:16.907 | INFO     | src.utils.log_config:_log_initialization_info:365 | 日志文件路径: logs/salary_system.log
2025-08-19 12:20:16.907 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-19 12:20:19.129 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-19 12:20:19.129 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-19 12:20:19.130 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-19 12:20:19.130 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-19 12:20:19.130 | INFO     | __main__:setup_app_logging:427 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-19 12:20:19.131 | INFO     | __main__:main:491 | 初始化核心管理器...
2025-08-19 12:20:19.131 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:20:19.131 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-19 12:20:19.132 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-19 12:20:19.132 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-19 12:20:19.133 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-19 12:20:19.140 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-19 12:20:19.140 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-19 12:20:19.141 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:20:19.145 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-19 12:20:19.145 | INFO     | __main__:main:496 | 核心管理器初始化完成。
2025-08-19 12:20:19.146 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-19 12:20:19.149 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-19 12:20:19.149 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:20:19.149 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-19 12:20:19.150 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-19 12:20:19.150 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-19 12:20:19.151 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-19 12:20:19.151 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11896 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-19 12:20:19.152 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:20:19.152 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11751 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-19 12:20:19.152 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11789 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-19 12:20:19.168 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-19 12:20:19.168 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-19 12:20:19.169 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:20:19.204 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-19 12:20:19.205 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-19 12:20:19.205 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-19 12:20:19.208 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:20:19.208 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-19 12:20:19.208 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-19 12:20:19.209 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-19 12:20:19.209 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:20:19.209 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-19 12:20:19.209 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 40.3ms
2025-08-19 12:20:19.216 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-19 12:20:19.216 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-19 12:20:19.216 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-19 12:20:19.216 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-19 12:20:19.216 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-19 12:20:19.216 | INFO     | src.gui.prototype.prototype_main_window:__init__:3590 | 🚀 性能管理器已集成
2025-08-19 12:20:19.217 | INFO     | src.gui.prototype.prototype_main_window:__init__:3592 | ✅ 新架构集成成功！
2025-08-19 12:20:19.218 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3705 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-19 12:20:19.218 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3670 | ✅ 新架构事件监听器设置完成
2025-08-19 12:20:19.218 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-19 12:20:19.218 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-19 12:20:19.218 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-19 12:20:19.722 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2666 | 菜单栏创建完成
2025-08-19 12:20:19.722 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-19 12:20:19.723 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-19 12:20:19.724 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-19 12:20:19.725 | INFO     | src.gui.prototype.prototype_main_window:__init__:2641 | 菜单栏管理器初始化完成
2025-08-19 12:20:19.726 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-19 12:20:19.726 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5376 | 管理器设置完成，包含增强版表头管理器
2025-08-19 12:20:19.726 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5381 | 🔧 开始应用窗口级Material Design样式...
2025-08-19 12:20:19.726 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-19 12:20:19.729 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-19 12:20:19.730 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5388 | ✅ 窗口级样式应用成功
2025-08-19 12:20:19.730 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5429 | ✅ 响应式样式监听设置完成
2025-08-19 12:20:19.734 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-19 12:20:19.739 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-19 12:20:19.739 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-19 12:20:19.742 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-19 12:20:19.742 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-19 12:20:19.745 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-19 12:20:19.786 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1911 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-19 12:20:19.786 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1870 | 使用兜底数据加载导航
2025-08-19 12:20:19.800 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-19 12:20:19.800 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-19 12:20:19.806 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 4个展开项
2025-08-19 12:20:19.807 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表']
2025-08-19 12:20:19.808 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-19 12:20:19.809 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-19 12:20:19.812 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-19 12:20:19.855 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1911 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-19 12:20:19.860 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-19 12:20:19.861 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-19 12:20:19.862 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-19 12:20:19.862 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-优化] 开始自动选择最新数据...
2025-08-19 12:20:19.862 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 12:20:19.864 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 12:20:19.864 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1403 | 🔧 [P1-优化] 暂未找到最新工资数据，可能需要先导入数据
2025-08-19 12:20:20.564 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-19 12:20:20.565 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-19 12:20:20.601 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-19 12:20:20.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-19 12:20:20.617 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-19 12:20:20.619 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-19 12:20:20.620 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-19 12:20:20.620 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2197 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-19 12:20:20.635 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-19 12:20:20.648 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-19 12:20:20.724 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:20:20.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2249 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-19 12:20:20.728 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-19 12:20:20.730 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-19 12:20:20.730 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2296 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-19 12:20:20.732 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-19 12:20:20.732 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-19 12:20:20.736 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-19 12:20:20.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-19 12:20:20.738 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-19 12:20:20.738 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2303 | 列宽管理器初始化完成
2025-08-19 12:20:20.739 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2430 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-19 12:20:20.743 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2317 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-19 12:20:20.744 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:20:20.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:20:20.745 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:20:20.749 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-19 12:20:20.749 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7594 | 方案A：安全设置列数: 0
2025-08-19 12:20:20.750 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2840 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-19 12:20:20.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:20:20.818 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-19 12:20:20.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2892 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-19 12:20:20.819 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:20:20.826 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:20:20.827 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:20:20.829 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:20:20.831 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:20:20.832 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 12:20:20.835 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-19 12:20:20.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 89.1ms
2025-08-19 12:20:20.840 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:20:20.842 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:20:20.844 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:20:20.846 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:20:20.847 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:20:20.870 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-19 12:20:20.915 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-19 12:20:20.916 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-19 12:20:21.048 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-19 12:20:21.297 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:20:21.297 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-19 12:20:21.297 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5338 | 快捷键设置完成
2025-08-19 12:20:21.298 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5295 | 主窗口UI设置完成。
2025-08-19 12:20:21.299 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5532 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-19 12:20:21.299 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5564 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-19 12:20:21.300 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5576 | ✅ 已连接分页刷新信号到主窗口
2025-08-19 12:20:21.300 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5577 | ✅ 已连接分页组件事件到新架构
2025-08-19 12:20:21.301 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5588 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-19 12:20:21.302 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5591 | 信号连接设置完成
2025-08-19 12:20:21.302 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6849 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-19 12:20:21.303 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-19 12:20:21.304 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-19 12:20:21.305 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6859 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-19 12:20:21.317 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:20:21.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:20:21.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:20:21.319 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:20:21.319 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:20:21.320 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7594 | 方案A：安全设置列数: 22
2025-08-19 12:20:21.322 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:20:21.322 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:20:21.323 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:20:21.324 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:20:21.325 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 5.9ms
2025-08-19 12:20:21.325 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:20:21.326 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:20:21.327 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:20:21.328 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:20:21.328 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:20:21.329 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:20:21.329 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8627 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-19 12:20:21.330 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:20:21.333 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:20:21.333 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:20:21.335 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:20:21.336 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:20:21.337 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:20:21.337 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:20:21.338 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:20:21.338 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:20:21.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 4.2ms
2025-08-19 12:20:21.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:20:21.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:20:21.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:20:21.343 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:20:21.345 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:20:21.346 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8648 | 已显示标准空表格，表头数量: 22
2025-08-19 12:20:21.347 | INFO     | src.gui.prototype.prototype_main_window:__init__:3644 | 原型主窗口初始化完成
2025-08-19 12:20:21.666 | INFO     | __main__:main:518 | 应用程序启动成功
2025-08-19 12:20:21.837 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1468 | 执行延迟的自动选择最新数据...
2025-08-19 12:20:21.839 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 12:20:21.840 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 12:20:21.841 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1491 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-19 12:20:21.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:20:21.862 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:20:21.862 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:20:21.863 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-19 12:20:21.865 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2072 | MainWorkspaceArea 响应式适配: sm
2025-08-19 12:20:22.371 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9532 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-19 12:20:22.371 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9442 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-19 12:20:22.372 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9456 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-19 12:20:22.373 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9990 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-19 12:20:22.390 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9462 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-19 12:21:04.392 | INFO     | src.utils.log_config:_log_initialization_info:356 | 日志系统初始化完成
2025-08-19 12:21:04.392 | INFO     | src.utils.log_config:_log_initialization_info:357 | 日志级别: INFO
2025-08-19 12:21:04.392 | INFO     | src.utils.log_config:_log_initialization_info:358 | 控制台输出: True
2025-08-19 12:21:04.393 | INFO     | src.utils.log_config:_log_initialization_info:359 | 文件输出: True
2025-08-19 12:21:04.393 | INFO     | src.utils.log_config:_log_initialization_info:365 | 日志文件路径: logs/salary_system.log
2025-08-19 12:21:04.393 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-19 12:21:20.252 | INFO     | src.utils.log_config:_log_initialization_info:356 | 日志系统初始化完成
2025-08-19 12:21:20.253 | INFO     | src.utils.log_config:_log_initialization_info:357 | 日志级别: INFO
2025-08-19 12:21:20.253 | INFO     | src.utils.log_config:_log_initialization_info:358 | 控制台输出: True
2025-08-19 12:21:20.253 | INFO     | src.utils.log_config:_log_initialization_info:359 | 文件输出: True
2025-08-19 12:21:20.253 | INFO     | src.utils.log_config:_log_initialization_info:365 | 日志文件路径: logs/salary_system.log
2025-08-19 12:21:20.254 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-19 12:21:21.831 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-19 12:21:21.831 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-19 12:21:21.831 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-19 12:21:21.832 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-19 12:21:21.832 | INFO     | __main__:setup_app_logging:427 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-19 12:21:21.833 | INFO     | __main__:main:491 | 初始化核心管理器...
2025-08-19 12:21:21.833 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:21:21.833 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-19 12:21:21.834 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-19 12:21:21.834 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-19 12:21:21.835 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-19 12:21:21.846 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-19 12:21:21.846 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-19 12:21:21.847 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:21:21.852 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-19 12:21:21.853 | INFO     | __main__:main:496 | 核心管理器初始化完成。
2025-08-19 12:21:21.854 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-19 12:21:21.854 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-19 12:21:21.854 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:21:21.855 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-19 12:21:21.855 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-19 12:21:21.855 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-19 12:21:21.856 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-19 12:21:21.856 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11896 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-19 12:21:21.857 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:21:21.857 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11751 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-19 12:21:21.857 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11789 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-19 12:21:21.880 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-19 12:21:21.899 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-19 12:21:21.903 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:21:21.906 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-19 12:21:21.907 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-19 12:21:21.907 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-19 12:21:21.907 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:21:21.907 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-19 12:21:21.907 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-19 12:21:21.907 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-19 12:21:21.907 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:21:21.907 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-19 12:21:21.907 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 4.1ms
2025-08-19 12:21:21.917 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-19 12:21:21.917 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-19 12:21:21.917 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-19 12:21:21.917 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-19 12:21:21.917 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-19 12:21:21.917 | INFO     | src.gui.prototype.prototype_main_window:__init__:3590 | 🚀 性能管理器已集成
2025-08-19 12:21:21.920 | INFO     | src.gui.prototype.prototype_main_window:__init__:3592 | ✅ 新架构集成成功！
2025-08-19 12:21:21.921 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3705 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-19 12:21:21.922 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3670 | ✅ 新架构事件监听器设置完成
2025-08-19 12:21:21.922 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-19 12:21:21.923 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-19 12:21:21.923 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-19 12:21:22.269 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2666 | 菜单栏创建完成
2025-08-19 12:21:22.270 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-19 12:21:22.270 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-19 12:21:22.271 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-19 12:21:22.271 | INFO     | src.gui.prototype.prototype_main_window:__init__:2641 | 菜单栏管理器初始化完成
2025-08-19 12:21:22.271 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-19 12:21:22.271 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5376 | 管理器设置完成，包含增强版表头管理器
2025-08-19 12:21:22.272 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5381 | 🔧 开始应用窗口级Material Design样式...
2025-08-19 12:21:22.272 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-19 12:21:22.275 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-19 12:21:22.276 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5388 | ✅ 窗口级样式应用成功
2025-08-19 12:21:22.276 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5429 | ✅ 响应式样式监听设置完成
2025-08-19 12:21:22.280 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-19 12:21:22.285 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-19 12:21:22.286 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-19 12:21:22.289 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-19 12:21:22.289 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-19 12:21:22.292 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-19 12:21:22.335 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1911 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-19 12:21:22.336 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1870 | 使用兜底数据加载导航
2025-08-19 12:21:22.336 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-19 12:21:22.336 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-19 12:21:22.350 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 4个展开项
2025-08-19 12:21:22.350 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > A岗职工', '工资表']
2025-08-19 12:21:22.351 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-19 12:21:22.351 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-19 12:21:22.354 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-19 12:21:22.358 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1911 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-19 12:21:22.360 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-19 12:21:22.360 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-19 12:21:22.361 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-19 12:21:22.362 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-优化] 开始自动选择最新数据...
2025-08-19 12:21:22.362 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 12:21:22.363 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 12:21:22.364 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1403 | 🔧 [P1-优化] 暂未找到最新工资数据，可能需要先导入数据
2025-08-19 12:21:23.045 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-19 12:21:23.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-19 12:21:23.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-19 12:21:23.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-19 12:21:23.059 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-19 12:21:23.059 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-19 12:21:23.060 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-19 12:21:23.060 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2197 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-19 12:21:23.060 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-19 12:21:23.061 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-19 12:21:23.061 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:21:23.061 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2249 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-19 12:21:23.065 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-19 12:21:23.066 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-19 12:21:23.067 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2296 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-19 12:21:23.067 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-19 12:21:23.067 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-19 12:21:23.068 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-19 12:21:23.069 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-19 12:21:23.069 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-19 12:21:23.083 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2303 | 列宽管理器初始化完成
2025-08-19 12:21:23.083 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2430 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-19 12:21:23.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2317 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-19 12:21:23.089 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:21:23.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:21:23.134 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:21:23.141 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-19 12:21:23.141 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7594 | 方案A：安全设置列数: 0
2025-08-19 12:21:23.147 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2840 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-19 12:21:23.148 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:21:23.152 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-19 12:21:23.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2892 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-19 12:21:23.153 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:21:23.159 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:21:23.159 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:21:23.160 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:21:23.161 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:21:23.161 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 12:21:23.165 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-19 12:21:23.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 29.1ms
2025-08-19 12:21:23.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:21:23.169 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:21:23.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:21:23.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:21:23.182 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:21:23.245 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-19 12:21:23.270 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-19 12:21:23.270 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-19 12:21:23.370 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-19 12:21:23.537 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:21:23.537 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-19 12:21:23.538 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5338 | 快捷键设置完成
2025-08-19 12:21:23.539 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5295 | 主窗口UI设置完成。
2025-08-19 12:21:23.541 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5532 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-19 12:21:23.542 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5564 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-19 12:21:23.544 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5576 | ✅ 已连接分页刷新信号到主窗口
2025-08-19 12:21:23.544 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5577 | ✅ 已连接分页组件事件到新架构
2025-08-19 12:21:23.547 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5588 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-19 12:21:23.548 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5591 | 信号连接设置完成
2025-08-19 12:21:23.549 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6849 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-19 12:21:23.550 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-19 12:21:23.573 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-19 12:21:23.581 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6859 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-19 12:21:23.597 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:21:23.598 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:21:23.598 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:21:23.599 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:21:23.599 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:21:23.600 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7594 | 方案A：安全设置列数: 22
2025-08-19 12:21:23.609 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:21:23.618 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:21:23.630 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:21:23.632 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:21:23.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 33.3ms
2025-08-19 12:21:23.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:21:23.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:21:23.636 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:21:23.636 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:21:23.637 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:21:23.637 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:21:23.638 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8627 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-19 12:21:23.638 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:21:23.639 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:21:23.640 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:21:23.643 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:21:23.644 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:21:23.645 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:21:23.645 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:21:23.646 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:21:23.646 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:21:23.656 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 12.4ms
2025-08-19 12:21:23.673 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:21:23.675 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:21:23.676 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:21:23.678 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:21:23.679 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:21:23.680 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8648 | 已显示标准空表格，表头数量: 22
2025-08-19 12:21:23.680 | INFO     | src.gui.prototype.prototype_main_window:__init__:3644 | 原型主窗口初始化完成
2025-08-19 12:21:23.862 | INFO     | __main__:main:518 | 应用程序启动成功
2025-08-19 12:21:23.867 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1468 | 执行延迟的自动选择最新数据...
2025-08-19 12:21:23.868 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 12:21:23.869 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 12:21:23.869 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1491 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-19 12:21:23.871 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:21:23.873 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:21:23.873 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:21:23.937 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-19 12:21:23.971 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2072 | MainWorkspaceArea 响应式适配: sm
2025-08-19 12:21:24.478 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9532 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-19 12:21:24.479 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9442 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-19 12:21:24.481 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9456 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-19 12:21:24.494 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9990 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-19 12:21:24.513 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9462 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-19 12:21:52.271 | CRITICAL | __main__:qt_aware_excepthook:167 | 🔧 [P0-CRITICAL] PyQt异常捕获: KeyboardInterrupt: 
2025-08-19 12:21:52.272 | CRITICAL | __main__:qt_aware_excepthook:168 | 🔧 [P0-CRITICAL] 异常堆栈:
Traceback (most recent call last):
  File "C:\test\salary_changes\salary_changes\src\gui\table_header_manager.py", line 598, in _auto_cleanup_deleted_tables
    def _auto_cleanup_deleted_tables(self):
    
KeyboardInterrupt

2025-08-19 12:21:52.273 | INFO     | __main__:qt_aware_excepthook:240 | 🔧 [P0-CRITICAL] 异常已处理，程序继续运行
2025-08-19 12:25:57.614 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > A岗职工', '工资表']
2025-08-19 12:25:57.615 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7292 | 导航变化: 工资表 > 2025年 > 05月 > 离休人员
2025-08-19 12:25:57.615 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10621 | 🚫 [用户要求] 表格切换不显示加载条:  -> None
2025-08-19 12:25:57.616 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10637 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 12:25:57.617 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8219 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '离休人员'] -> salary_data_2025_05_retired_employees
2025-08-19 12:25:57.618 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:25:57.618 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_retired_employees 的缓存
2025-08-19 12:25:57.620 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11307 | 已注册 2 个表格到表头管理器
2025-08-19 12:25:57.620 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-19 12:25:57.621 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 1.04ms
2025-08-19 12:25:57.621 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-19 12:25:57.621 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7508 | 🆕 使用新架构加载数据: salary_data_2025_05_retired_employees（通过事件系统）
2025-08-19 12:25:57.622 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-19 12:25:57.622 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 12:25:57.623 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 12:25:57.624 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7517 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_retired_employees 尚未创建，显示空表格等待数据导入
2025-08-19 12:25:57.628 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8526 | 🔧 [P2-优化] 使用统一表头: 15个字段，表类型: retired_employees
2025-08-19 12:25:57.629 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8623 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_retired_employees 的专用表头: 15个字段
2025-08-19 12:25:57.632 | INFO     | src.gui.prototype.prototype_main_window:set_data:845 | 空数据输入发生 3 次（2s 窗口），将显示空表提示
2025-08-19 12:25:57.632 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:25:57.632 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:25:57.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:25:57.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:25:57.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:25:57.634 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:25:57.634 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:25:57.634 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:25:57.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:25:57.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 12:25:57.636 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 3.4ms
2025-08-19 12:25:57.640 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:25:57.640 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:25:57.641 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:25:57.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:25:57.642 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:25:57.643 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:25:57.643 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8648 | 已显示标准空表格，表头数量: 15
2025-08-19 12:25:57.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:25:58.512 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > A岗职工', '工资表']
2025-08-19 12:25:58.513 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7292 | 导航变化: 工资表 > 2025年 > 05月 > 退休人员
2025-08-19 12:25:58.513 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10621 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_retired_employees -> None
2025-08-19 12:25:58.514 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10637 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 12:25:58.515 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8219 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '退休人员'] -> salary_data_2025_05_pension_employees
2025-08-19 12:25:58.515 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:25:58.516 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_pension_employees 的缓存
2025-08-19 12:25:58.518 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11307 | 已注册 2 个表格到表头管理器
2025-08-19 12:25:58.518 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7508 | 🆕 使用新架构加载数据: salary_data_2025_05_pension_employees（通过事件系统）
2025-08-19 12:25:58.519 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 12:25:58.520 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 12:25:58.520 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7517 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_pension_employees 尚未创建，显示空表格等待数据导入
2025-08-19 12:25:58.521 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8526 | 🔧 [P2-优化] 使用统一表头: 26个字段，表类型: pension_employees
2025-08-19 12:25:58.521 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8623 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_pension_employees 的专用表头: 26个字段
2025-08-19 12:25:58.523 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:25:58.523 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:25:58.524 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:25:58.525 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:25:58.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:25:58.526 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:25:58.526 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:25:58.527 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:25:58.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:25:58.528 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 3.1ms
2025-08-19 12:25:58.528 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:25:58.528 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:25:58.530 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:25:58.530 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:25:58.531 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:25:58.532 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8648 | 已显示标准空表格，表头数量: 26
2025-08-19 12:25:58.631 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:25:59.083 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表']
2025-08-19 12:25:59.083 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7292 | 导航变化: 工资表 > 2025年 > 05月 > A岗职工
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10621 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_pension_employees -> None
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10637 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8219 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', 'A岗职工'] -> salary_data_2025_05_a_grade_employees
2025-08-19 12:25:59.098 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:25:59.098 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_a_grade_employees 的缓存
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11307 | 已注册 2 个表格到表头管理器
2025-08-19 12:25:59.098 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-19 12:25:59.098 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-08-19 12:25:59.098 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7508 | 🆕 使用新架构加载数据: salary_data_2025_05_a_grade_employees（通过事件系统）
2025-08-19 12:25:59.098 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-19 12:25:59.098 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 12:25:59.098 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7517 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_a_grade_employees 尚未创建，显示空表格等待数据导入
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8526 | 🔧 [P2-优化] 使用统一表头: 20个字段，表类型: a_grade_employees
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8623 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_a_grade_employees 的专用表头: 20个字段
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:25:59.098 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 12:25:59.112 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 13.7ms
2025-08-19 12:25:59.114 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:25:59.124 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:25:59.127 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:25:59.128 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:25:59.129 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:25:59.131 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:25:59.135 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8648 | 已显示标准空表格，表头数量: 20
2025-08-19 12:25:59.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:26:01.353 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2072 | MainWorkspaceArea 响应式适配: sm
2025-08-19 12:26:01.353 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-优化] 开始自动选择最新数据...
2025-08-19 12:26:01.353 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 12:26:01.353 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 12:26:01.353 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1403 | 🔧 [P1-优化] 暂未找到最新工资数据，可能需要先导入数据
2025-08-19 12:26:02.734 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-19 12:26:02.735 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5825 | 🔧 [P0-修复] 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 05月 > A岗职工。打开导入对话框。
2025-08-19 12:26:02.736 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5834 | 🔧 [P0-修复] 开始创建数据导入对话框...
2025-08-19 12:26:02.736 | INFO     | src.gui.main_dialogs:__init__:55 | 🔧 [P0-修复] 开始初始化数据导入对话框...
2025-08-19 12:26:02.736 | INFO     | src.gui.main_dialogs:__init__:71 | 🔧 [P0-修复] 初始化导入器和验证器...
2025-08-19 12:26:02.737 | INFO     | src.gui.main_dialogs:__init__:76 | 🔧 [P0-修复] 初始化多Sheet导入器...
2025-08-19 12:26:02.737 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-19 12:26:02.739 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:75 | 多Sheet导入器初始化完成
2025-08-19 12:26:02.739 | INFO     | src.gui.main_dialogs:__init__:80 | 🔧 [P0-修复] 初始化默认设置管理器...
2025-08-19 12:26:02.740 | INFO     | src.gui.main_dialogs:__init__:85 | 🔧 [P0-修复] 开始初始化用户界面...
2025-08-19 12:26:02.740 | INFO     | src.gui.main_dialogs:_init_ui:110 | 🔧 [P0-修复] 设置对话框基本属性...
2025-08-19 12:26:02.740 | INFO     | src.gui.main_dialogs:_init_ui:120 | 🔧 [P0-修复] 创建主布局...
2025-08-19 12:26:02.741 | INFO     | src.gui.main_dialogs:_init_ui:125 | 🔧 [P0-修复] 创建目标选择组件...
2025-08-19 12:26:02.741 | INFO     | src.gui.widgets.target_selection_widget:__init__:112 | 🔧 [P0-修复] 开始初始化目标选择组件...
2025-08-19 12:26:02.741 | INFO     | src.gui.widgets.target_selection_widget:__init__:128 | 🔧 [P0-修复] 加载可选项配置...
2025-08-19 12:26:02.742 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:272 | 成功加载导航配置
2025-08-19 12:26:02.742 | INFO     | src.gui.widgets.target_selection_widget:__init__:132 | 🔧 [P0-修复] 初始化目标选择组件UI...
2025-08-19 12:26:02.755 | INFO     | src.gui.widgets.target_selection_widget:__init__:136 | 🔧 [P0-修复] 加载初始目标...
2025-08-19 12:26:02.755 | INFO     | src.gui.widgets.target_selection_widget:__init__:139 | 🔧 [P0-修复] 目标选择组件初始化完成
2025-08-19 12:26:02.755 | INFO     | src.gui.main_dialogs:_init_ui:130 | 🔧 [P0-修复] 设置目标路径: 工资表 > 2025年 > 05月 > A岗职工
2025-08-19 12:26:02.755 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:527 | 从路径设置目标: 工资表 > 2025年 > 05月 > A岗职工
2025-08-19 12:26:02.755 | ERROR    | src.gui.main_dialogs:__init__:101 | 🔧 [P0-修复] 数据导入对话框初始化失败: 'QComboBox' object has no attribute 'currentDataChanged'
2025-08-19 12:26:04.466 | ERROR    | src.gui.prototype.prototype_main_window:_on_import_data_requested:5875 | 🔧 [P0-修复] 打开数据导入对话框失败: 'QComboBox' object has no attribute 'currentDataChanged'
2025-08-19 12:26:07.840 | INFO     | __main__:main:523 | 应用程序正常退出
2025-08-19 12:26:07.843 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_0_1824918706448 已自动清理（弱引用回调）
2025-08-19 12:35:01.388 | INFO     | src.utils.log_config:_log_initialization_info:356 | 日志系统初始化完成
2025-08-19 12:35:01.388 | INFO     | src.utils.log_config:_log_initialization_info:357 | 日志级别: INFO
2025-08-19 12:35:01.388 | INFO     | src.utils.log_config:_log_initialization_info:358 | 控制台输出: True
2025-08-19 12:35:01.388 | INFO     | src.utils.log_config:_log_initialization_info:359 | 文件输出: True
2025-08-19 12:35:01.389 | INFO     | src.utils.log_config:_log_initialization_info:365 | 日志文件路径: logs/salary_system.log
2025-08-19 12:35:01.389 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-19 12:35:03.815 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-19 12:35:03.815 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-19 12:35:03.816 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-19 12:35:03.818 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-19 12:35:03.819 | INFO     | __main__:setup_app_logging:427 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-19 12:35:03.819 | INFO     | __main__:main:491 | 初始化核心管理器...
2025-08-19 12:35:03.820 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:35:03.821 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-19 12:35:03.822 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-19 12:35:03.822 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-19 12:35:03.823 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-19 12:35:03.857 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-19 12:35:03.857 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-19 12:35:03.858 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:35:03.863 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-19 12:35:03.863 | INFO     | __main__:main:496 | 核心管理器初始化完成。
2025-08-19 12:35:03.865 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-19 12:35:03.868 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-19 12:35:03.868 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:35:03.869 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-19 12:35:03.869 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-19 12:35:03.870 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-19 12:35:03.871 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-19 12:35:03.871 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11900 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-19 12:35:03.872 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:35:03.872 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11755 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-19 12:35:03.873 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11793 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-19 12:35:03.889 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-19 12:35:03.890 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-19 12:35:03.892 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:35:03.895 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-19 12:35:03.896 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-19 12:35:03.897 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-19 12:35:03.897 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:35:03.897 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-19 12:35:03.898 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-19 12:35:03.899 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-19 12:35:03.906 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:35:03.906 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-19 12:35:03.906 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 14.3ms
2025-08-19 12:35:03.934 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-19 12:35:03.935 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-19 12:35:03.935 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-19 12:35:03.935 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-19 12:35:03.935 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-19 12:35:03.935 | INFO     | src.gui.prototype.prototype_main_window:__init__:3590 | 🚀 性能管理器已集成
2025-08-19 12:35:03.936 | INFO     | src.gui.prototype.prototype_main_window:__init__:3592 | ✅ 新架构集成成功！
2025-08-19 12:35:03.936 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3705 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-19 12:35:03.937 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3670 | ✅ 新架构事件监听器设置完成
2025-08-19 12:35:03.938 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-19 12:35:03.940 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-19 12:35:03.940 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-19 12:35:04.260 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2666 | 菜单栏创建完成
2025-08-19 12:35:04.261 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-19 12:35:04.261 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-19 12:35:04.262 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-19 12:35:04.262 | INFO     | src.gui.prototype.prototype_main_window:__init__:2641 | 菜单栏管理器初始化完成
2025-08-19 12:35:04.262 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-19 12:35:04.262 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5376 | 管理器设置完成，包含增强版表头管理器
2025-08-19 12:35:04.263 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5381 | 🔧 开始应用窗口级Material Design样式...
2025-08-19 12:35:04.263 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-19 12:35:04.267 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-19 12:35:04.268 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5388 | ✅ 窗口级样式应用成功
2025-08-19 12:35:04.268 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5429 | ✅ 响应式样式监听设置完成
2025-08-19 12:35:04.272 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-19 12:35:04.273 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-19 12:35:04.275 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-19 12:35:04.277 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-19 12:35:04.278 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-19 12:35:04.280 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-19 12:35:04.290 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1911 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-19 12:35:04.331 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1870 | 使用兜底数据加载导航
2025-08-19 12:35:04.332 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-19 12:35:04.333 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-19 12:35:04.339 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表']
2025-08-19 12:35:04.340 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 4个展开项
2025-08-19 12:35:04.342 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表']
2025-08-19 12:35:04.344 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-19 12:35:04.345 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-19 12:35:04.347 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-19 12:35:04.351 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1911 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-19 12:35:04.355 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-19 12:35:04.355 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-19 12:35:04.390 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-19 12:35:04.391 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-优化] 开始自动选择最新数据...
2025-08-19 12:35:04.392 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 12:35:04.393 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 12:35:04.394 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1403 | 🔧 [P1-优化] 暂未找到最新工资数据，可能需要先导入数据
2025-08-19 12:35:04.731 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-19 12:35:04.732 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-19 12:35:04.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-19 12:35:04.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-19 12:35:04.757 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-19 12:35:04.757 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-19 12:35:04.758 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-19 12:35:04.758 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2197 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-19 12:35:04.759 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-19 12:35:04.760 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-19 12:35:04.761 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:35:04.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2249 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-19 12:35:04.836 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-19 12:35:04.840 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-19 12:35:04.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2296 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-19 12:35:04.842 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-19 12:35:04.843 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-19 12:35:04.844 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-19 12:35:04.844 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-19 12:35:04.845 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-19 12:35:04.846 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2303 | 列宽管理器初始化完成
2025-08-19 12:35:04.846 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2430 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-19 12:35:04.848 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2317 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-19 12:35:04.849 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:35:04.849 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:35:04.850 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:35:04.855 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-19 12:35:04.855 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7594 | 方案A：安全设置列数: 0
2025-08-19 12:35:04.858 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2840 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-19 12:35:04.858 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:35:04.861 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-19 12:35:04.862 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2892 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-19 12:35:04.862 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:35:04.867 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:35:04.868 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:35:04.880 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:35:04.887 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:35:04.891 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 12:35:04.935 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-19 12:35:04.949 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 82.8ms
2025-08-19 12:35:05.026 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:35:05.028 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:35:05.033 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:35:05.035 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:35:05.036 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:35:05.111 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-19 12:35:05.120 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-19 12:35:05.125 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-19 12:35:05.297 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-19 12:35:05.658 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:35:05.659 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-19 12:35:05.659 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5338 | 快捷键设置完成
2025-08-19 12:35:05.662 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5295 | 主窗口UI设置完成。
2025-08-19 12:35:05.663 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5532 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-19 12:35:05.665 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5564 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-19 12:35:05.665 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5576 | ✅ 已连接分页刷新信号到主窗口
2025-08-19 12:35:05.666 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5577 | ✅ 已连接分页组件事件到新架构
2025-08-19 12:35:05.668 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5588 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-19 12:35:05.669 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5591 | 信号连接设置完成
2025-08-19 12:35:05.670 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6849 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-19 12:35:05.671 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-19 12:35:05.729 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-19 12:35:05.730 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6859 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-19 12:35:05.742 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:35:05.742 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:35:05.743 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:35:05.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:35:05.745 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:35:05.747 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7594 | 方案A：安全设置列数: 22
2025-08-19 12:35:05.748 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:35:05.748 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:35:05.750 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:35:05.764 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:35:05.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 41.9ms
2025-08-19 12:35:05.788 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:35:05.789 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:35:05.791 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:35:05.792 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:35:05.793 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:35:05.795 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:35:05.796 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8631 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 23个字段
2025-08-19 12:35:05.797 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:35:05.799 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:35:05.803 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:35:05.804 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:35:05.804 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:35:05.805 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:35:05.806 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:35:05.806 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:35:05.807 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:35:05.808 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 4.2ms
2025-08-19 12:35:05.808 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:35:05.810 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:35:05.811 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:35:05.812 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:35:05.864 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:35:05.865 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8652 | 已显示标准空表格，表头数量: 23
2025-08-19 12:35:05.866 | INFO     | src.gui.prototype.prototype_main_window:__init__:3644 | 原型主窗口初始化完成
2025-08-19 12:35:06.043 | INFO     | __main__:main:518 | 应用程序启动成功
2025-08-19 12:35:06.054 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:35:06.055 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1468 | 执行延迟的自动选择最新数据...
2025-08-19 12:35:06.055 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 12:35:06.057 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 12:35:06.058 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1491 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-19 12:35:06.060 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:35:06.061 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:35:06.159 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-19 12:35:06.166 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2072 | MainWorkspaceArea 响应式适配: sm
2025-08-19 12:35:06.672 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9536 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-19 12:35:06.673 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9446 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-19 12:35:06.674 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9460 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-19 12:35:06.675 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9994 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-19 12:35:06.689 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9466 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-19 12:35:48.751 | INFO     | src.utils.log_config:_log_initialization_info:356 | 日志系统初始化完成
2025-08-19 12:35:48.752 | INFO     | src.utils.log_config:_log_initialization_info:357 | 日志级别: INFO
2025-08-19 12:35:48.752 | INFO     | src.utils.log_config:_log_initialization_info:358 | 控制台输出: True
2025-08-19 12:35:48.752 | INFO     | src.utils.log_config:_log_initialization_info:359 | 文件输出: True
2025-08-19 12:35:48.753 | INFO     | src.utils.log_config:_log_initialization_info:365 | 日志文件路径: logs/salary_system.log
2025-08-19 12:35:48.753 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-19 12:36:19.966 | INFO     | src.utils.log_config:_log_initialization_info:356 | 日志系统初始化完成
2025-08-19 12:36:19.966 | INFO     | src.utils.log_config:_log_initialization_info:357 | 日志级别: INFO
2025-08-19 12:36:19.966 | INFO     | src.utils.log_config:_log_initialization_info:358 | 控制台输出: True
2025-08-19 12:36:19.967 | INFO     | src.utils.log_config:_log_initialization_info:359 | 文件输出: True
2025-08-19 12:36:19.967 | INFO     | src.utils.log_config:_log_initialization_info:365 | 日志文件路径: logs/salary_system.log
2025-08-19 12:36:19.968 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-19 12:36:21.553 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-19 12:36:21.553 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-19 12:36:21.554 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-19 12:36:21.554 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-19 12:36:21.555 | INFO     | __main__:setup_app_logging:427 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-19 12:36:21.555 | INFO     | __main__:main:491 | 初始化核心管理器...
2025-08-19 12:36:21.556 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:36:21.556 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-19 12:36:21.557 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-19 12:36:21.558 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-19 12:36:21.559 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-19 12:36:21.578 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-19 12:36:21.590 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-19 12:36:21.590 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:36:21.595 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-19 12:36:21.595 | INFO     | __main__:main:496 | 核心管理器初始化完成。
2025-08-19 12:36:21.597 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-19 12:36:21.597 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-19 12:36:21.597 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:36:21.597 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-19 12:36:21.597 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-19 12:36:21.599 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-19 12:36:21.599 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-19 12:36:21.599 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11900 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-19 12:36:21.599 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:36:21.599 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11755 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-19 12:36:21.599 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11793 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-19 12:36:21.618 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-19 12:36:21.619 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-19 12:36:21.620 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 12:36:21.657 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-19 12:36:21.663 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-19 12:36:21.663 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-19 12:36:21.663 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:36:21.663 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-19 12:36:21.663 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-19 12:36:21.663 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-19 12:36:21.663 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:36:21.663 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-19 12:36:21.663 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 43.1ms
2025-08-19 12:36:21.663 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-19 12:36:21.663 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-19 12:36:21.663 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-19 12:36:21.663 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-19 12:36:21.663 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-19 12:36:21.663 | INFO     | src.gui.prototype.prototype_main_window:__init__:3590 | 🚀 性能管理器已集成
2025-08-19 12:36:21.679 | INFO     | src.gui.prototype.prototype_main_window:__init__:3592 | ✅ 新架构集成成功！
2025-08-19 12:36:21.725 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3705 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-19 12:36:21.726 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3670 | ✅ 新架构事件监听器设置完成
2025-08-19 12:36:21.728 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-19 12:36:21.729 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-19 12:36:21.729 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-19 12:36:22.081 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2666 | 菜单栏创建完成
2025-08-19 12:36:22.082 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-19 12:36:22.082 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-19 12:36:22.082 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-19 12:36:22.083 | INFO     | src.gui.prototype.prototype_main_window:__init__:2641 | 菜单栏管理器初始化完成
2025-08-19 12:36:22.083 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-19 12:36:22.083 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5376 | 管理器设置完成，包含增强版表头管理器
2025-08-19 12:36:22.084 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5381 | 🔧 开始应用窗口级Material Design样式...
2025-08-19 12:36:22.084 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-19 12:36:22.087 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-19 12:36:22.087 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5388 | ✅ 窗口级样式应用成功
2025-08-19 12:36:22.088 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5429 | ✅ 响应式样式监听设置完成
2025-08-19 12:36:22.093 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-19 12:36:22.096 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-19 12:36:22.097 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-19 12:36:22.098 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-19 12:36:22.099 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-19 12:36:22.135 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-19 12:36:22.141 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1911 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-19 12:36:22.157 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1870 | 使用兜底数据加载导航
2025-08-19 12:36:22.161 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-19 12:36:22.163 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-19 12:36:22.171 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月']
2025-08-19 12:36:22.172 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 4个展开项
2025-08-19 12:36:22.173 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月']
2025-08-19 12:36:22.174 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-19 12:36:22.175 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-19 12:36:22.196 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-19 12:36:22.220 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1911 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-19 12:36:22.224 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-19 12:36:22.234 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-19 12:36:22.236 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-19 12:36:22.238 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-优化] 开始自动选择最新数据...
2025-08-19 12:36:22.275 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 12:36:22.276 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 12:36:22.277 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1403 | 🔧 [P1-优化] 暂未找到最新工资数据，可能需要先导入数据
2025-08-19 12:36:22.801 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-19 12:36:22.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-19 12:36:22.809 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-19 12:36:22.811 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-19 12:36:22.816 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-19 12:36:22.817 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-19 12:36:22.819 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-19 12:36:22.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2197 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-19 12:36:22.821 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-19 12:36:22.822 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-19 12:36:22.823 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 12:36:22.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2249 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-19 12:36:22.828 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-19 12:36:22.830 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-19 12:36:22.831 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2296 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-19 12:36:22.832 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-19 12:36:22.832 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-19 12:36:22.883 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-19 12:36:22.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-19 12:36:22.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-19 12:36:22.887 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2303 | 列宽管理器初始化完成
2025-08-19 12:36:22.887 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2430 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-19 12:36:22.889 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2317 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-19 12:36:22.890 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:36:22.890 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:36:22.892 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:36:22.897 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-19 12:36:22.897 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7594 | 方案A：安全设置列数: 0
2025-08-19 12:36:22.898 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2840 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-19 12:36:22.898 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:36:22.914 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-19 12:36:22.918 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2892 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-19 12:36:22.931 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:36:22.936 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:36:22.936 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:36:22.936 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:36:22.937 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:36:22.937 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 12:36:22.940 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-19 12:36:22.944 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 46.9ms
2025-08-19 12:36:22.944 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:36:22.945 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:36:22.975 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:36:22.976 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:36:22.977 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:36:22.989 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-19 12:36:23.014 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-19 12:36:23.025 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-19 12:36:23.103 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-19 12:36:23.266 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 12:36:23.266 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-19 12:36:23.266 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5338 | 快捷键设置完成
2025-08-19 12:36:23.269 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5295 | 主窗口UI设置完成。
2025-08-19 12:36:23.273 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5532 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-19 12:36:23.274 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5564 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-19 12:36:23.274 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5576 | ✅ 已连接分页刷新信号到主窗口
2025-08-19 12:36:23.275 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5577 | ✅ 已连接分页组件事件到新架构
2025-08-19 12:36:23.276 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5588 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-19 12:36:23.276 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5591 | 信号连接设置完成
2025-08-19 12:36:23.277 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6849 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-19 12:36:23.278 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-19 12:36:23.279 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-19 12:36:23.279 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6859 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-19 12:36:23.288 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:36:23.289 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:36:23.289 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:36:23.290 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:36:23.292 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:36:23.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7594 | 方案A：安全设置列数: 22
2025-08-19 12:36:23.294 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:36:23.294 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:36:23.295 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:36:23.295 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:36:23.296 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 5.8ms
2025-08-19 12:36:23.297 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:36:23.297 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:36:23.298 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:36:23.298 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:36:23.299 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:36:23.299 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 12:36:23.300 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8631 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 23个字段
2025-08-19 12:36:23.301 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 12:36:23.302 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 12:36:23.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 12:36:23.304 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 12:36:23.304 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 12:36:23.306 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 12:36:23.306 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 12:36:23.306 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 12:36:23.307 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 12:36:23.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 3.2ms
2025-08-19 12:36:23.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 12:36:23.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 12:36:23.310 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 12:36:23.310 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 12:36:23.310 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 12:36:23.311 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8652 | 已显示标准空表格，表头数量: 23
2025-08-19 12:36:23.312 | INFO     | src.gui.prototype.prototype_main_window:__init__:3644 | 原型主窗口初始化完成
2025-08-19 12:36:23.501 | INFO     | __main__:main:518 | 应用程序启动成功
2025-08-19 12:36:23.527 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1468 | 执行延迟的自动选择最新数据...
2025-08-19 12:36:23.529 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 12:36:23.531 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 12:36:23.532 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1491 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-19 12:36:23.533 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:36:23.535 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:36:23.536 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 12:36:23.599 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-19 12:36:23.607 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2072 | MainWorkspaceArea 响应式适配: sm
2025-08-19 12:36:24.118 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9536 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-19 12:36:24.118 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9446 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-19 12:36:24.119 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9460 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-19 12:36:24.119 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9994 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-19 12:36:24.138 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9466 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-19 12:36:57.172 | CRITICAL | __main__:qt_aware_excepthook:167 | 🔧 [P0-CRITICAL] PyQt异常捕获: KeyboardInterrupt: 
2025-08-19 12:36:57.172 | CRITICAL | __main__:qt_aware_excepthook:168 | 🔧 [P0-CRITICAL] 异常堆栈:
Traceback (most recent call last):
  File "C:\test\salary_changes\salary_changes\src\gui\prototype\widgets\enhanced_navigation_panel.py", line 681, in _update_statistics_safe
    def _update_statistics_safe(self):
    
KeyboardInterrupt

2025-08-19 12:36:57.173 | INFO     | __main__:qt_aware_excepthook:240 | 🔧 [P0-CRITICAL] 异常已处理，程序继续运行
2025-08-19 12:45:30.376 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-19 12:45:30.376 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8243 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-19 12:45:30.376 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5825 | 🔧 [P0-修复] 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-19 12:45:30.376 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5834 | 🔧 [P0-修复] 开始创建数据导入对话框...
2025-08-19 12:45:30.376 | INFO     | src.gui.main_dialogs:__init__:55 | 🔧 [P0-修复] 开始初始化数据导入对话框...
2025-08-19 12:45:30.376 | INFO     | src.gui.main_dialogs:__init__:71 | 🔧 [P0-修复] 初始化导入器和验证器...
2025-08-19 12:45:30.376 | INFO     | src.gui.main_dialogs:__init__:76 | 🔧 [P0-修复] 初始化多Sheet导入器...
2025-08-19 12:45:30.376 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-19 12:45:30.376 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:75 | 多Sheet导入器初始化完成
2025-08-19 12:45:30.376 | INFO     | src.gui.main_dialogs:__init__:80 | 🔧 [P0-修复] 初始化默认设置管理器...
2025-08-19 12:45:30.376 | INFO     | src.gui.main_dialogs:__init__:85 | 🔧 [P0-修复] 开始初始化用户界面...
2025-08-19 12:45:30.376 | INFO     | src.gui.main_dialogs:_init_ui:110 | 🔧 [P0-修复] 设置对话框基本属性...
2025-08-19 12:45:30.376 | INFO     | src.gui.main_dialogs:_init_ui:120 | 🔧 [P0-修复] 创建主布局...
2025-08-19 12:45:30.376 | INFO     | src.gui.main_dialogs:_init_ui:125 | 🔧 [P0-修复] 创建目标选择组件...
2025-08-19 12:45:30.376 | INFO     | src.gui.widgets.target_selection_widget:__init__:112 | 🔧 [P0-修复] 开始初始化目标选择组件...
2025-08-19 12:45:30.376 | INFO     | src.gui.widgets.target_selection_widget:__init__:128 | 🔧 [P0-修复] 加载可选项配置...
2025-08-19 12:45:30.376 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:272 | 成功加载导航配置
2025-08-19 12:45:30.376 | INFO     | src.gui.widgets.target_selection_widget:__init__:132 | 🔧 [P0-修复] 初始化目标选择组件UI...
2025-08-19 12:45:30.395 | INFO     | src.gui.widgets.target_selection_widget:__init__:136 | 🔧 [P0-修复] 加载初始目标...
2025-08-19 12:45:30.395 | INFO     | src.gui.widgets.target_selection_widget:__init__:139 | 🔧 [P0-修复] 目标选择组件初始化完成
2025-08-19 12:45:30.395 | INFO     | src.gui.main_dialogs:_init_ui:130 | 🔧 [P0-修复] 设置目标路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-19 12:45:30.395 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:527 | 从路径设置目标: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-19 12:45:30.395 | ERROR    | src.gui.main_dialogs:__init__:101 | 🔧 [P0-修复] 数据导入对话框初始化失败: 'QComboBox' object has no attribute 'currentDataChanged'
2025-08-19 12:45:31.672 | ERROR    | src.gui.prototype.prototype_main_window:_on_import_data_requested:5875 | 🔧 [P0-修复] 打开数据导入对话框失败: 'QComboBox' object has no attribute 'currentDataChanged'
2025-08-19 12:45:33.901 | INFO     | __main__:main:523 | 应用程序正常退出
