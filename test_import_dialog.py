#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据导入对话框的显示功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from src.gui.main_dialogs import DataImportDialog
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.utils.log_config import setup_logger

class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(__name__)
        self.init_ui()
        self.init_managers()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("数据导入对话框测试")
        self.setGeometry(100, 100, 400, 200)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 测试按钮
        self.test_button = QPushButton("测试数据导入对话框")
        self.test_button.clicked.connect(self.test_import_dialog)
        layout.addWidget(self.test_button)
    
    def init_managers(self):
        """初始化管理器"""
        try:
            # 初始化数据库管理器
            self.db_manager = DatabaseManager()
            
            # 初始化动态表格管理器
            self.dynamic_table_manager = DynamicTableManager(self.db_manager)
            
            self.logger.info("管理器初始化成功")
            
        except Exception as e:
            self.logger.error(f"管理器初始化失败: {e}")
            self.dynamic_table_manager = None
    
    def test_import_dialog(self):
        """测试数据导入对话框"""
        try:
            self.logger.info("🔧 [测试] 开始测试数据导入对话框...")
            
            if not self.dynamic_table_manager:
                self.logger.error("🔧 [测试] dynamic_table_manager 未初始化")
                return
            
            # 创建数据导入对话框
            self.logger.info("🔧 [测试] 创建数据导入对话框...")
            dialog = DataImportDialog(
                parent=self,
                dynamic_table_manager=self.dynamic_table_manager,
                target_path="工资表 > 2025年 > 08月 > 全部在职人员"
            )
            
            self.logger.info("🔧 [测试] 对话框创建成功，开始显示...")
            
            # 设置对话框属性
            dialog.setModal(True)
            dialog.setWindowFlags(dialog.windowFlags() | Qt.WindowStaysOnTopHint)
            dialog.show()
            dialog.raise_()
            dialog.activateWindow()
            
            # 执行对话框
            result = dialog.exec_()
            self.logger.info(f"🔧 [测试] 对话框执行完成，结果: {result}")
            
        except Exception as e:
            self.logger.error(f"🔧 [测试] 测试失败: {e}", exc_info=True)
            print(f"测试失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("数据导入对话框测试")
    app.setApplicationVersion("1.0.0")
    
    # 创建主窗口
    window = TestMainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
