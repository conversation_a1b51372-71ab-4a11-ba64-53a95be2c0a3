#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
P0和P1级优化综合测试脚本
测试所有修复和优化的效果
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
import pandas as pd
import time
from typing import List, Dict, Any

def test_header_cache():
    """测试表头缓存功能"""
    print("\n" + "="*60)
    print("测试1: 表头缓存机制")
    print("="*60)
    
    try:
        from src.gui.prototype.widgets.table_header_cache import get_header_cache
        
        cache = get_header_cache()
        
        # 测试缓存存储
        headers1 = ['工号', '姓名', '部门', '工资']
        display_headers1 = ['Employee ID', 'Name', 'Department', 'Salary']
        
        print("1. 缓存第一个表...")
        entry1 = cache.cache_headers('table1', headers1, display_headers1)
        print(f"   缓存成功: 版本={entry1.version}, 列数={entry1.column_count}")
        
        # 测试缓存命中
        print("2. 测试缓存命中...")
        cached = cache.get_cached_headers('table1', headers1)
        if cached:
            print(f"   缓存命中: 访问次数={cached.access_count}")
        else:
            print("   [错误] 缓存未命中")
            return False
        
        # 测试表头验证
        print("3. 测试表头验证...")
        
        # 正常表头
        is_valid, msg = cache.validate_headers('table1', headers1)
        print(f"   正常表头验证: {is_valid}")
        
        # 异常表头（大量重复）
        bad_headers = ['工号'] * 50 + ['姓名'] * 50
        is_valid, msg = cache.validate_headers('table2', bad_headers)
        print(f"   异常表头验证: {is_valid}, 原因: {msg}")
        
        # 测试版本控制
        print("4. 测试版本控制...")
        new_headers = ['工号', '姓名', '部门', '工资', '奖金']
        entry2 = cache.cache_headers('table1', new_headers)
        print(f"   表头更新后版本: {entry2.version}")
        
        # 获取统计信息
        stats = cache.get_statistics()
        print(f"5. 缓存统计: 总条目={stats['total_entries']}, 总访问={stats['total_accesses']}")
        
        print("\n[成功] 表头缓存测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 表头缓存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pagination_state():
    """测试分页状态管理"""
    print("\n" + "="*60)
    print("测试2: 分页状态管理")
    print("="*60)
    
    try:
        from src.gui.prototype.widgets.pagination_state_manager import get_pagination_manager
        
        manager = get_pagination_manager()
        
        # 清理旧状态
        manager.clear_all_states()
        
        print("1. 测试表切换判断...")
        is_switch = manager.is_table_switch(None, 'table1')
        print(f"   首次加载是否为表切换: {is_switch}")
        
        is_switch = manager.is_table_switch('table1', 'table1')
        print(f"   同表操作是否为表切换: {is_switch}")
        
        is_switch = manager.is_table_switch('table1', 'table2')
        print(f"   不同表是否为表切换: {is_switch}")
        
        print("2. 测试分页状态...")
        state = manager.set_state('table1', 
                                 current_page=1, 
                                 page_size=50, 
                                 total_records=1000)
        print(f"   设置状态: 页码={state.current_page}, 总页数={state.total_pages}")
        
        # 测试分页操作
        print("3. 测试分页操作...")
        success = manager.begin_pagination('table1')
        print(f"   开始分页: {success}")
        
        is_paging = manager.is_pagination('table1', 2)
        print(f"   切换到第2页是否为分页: {is_paging}")
        
        manager.end_pagination('table1')
        print("   结束分页")
        
        # 获取统计
        stats = manager.get_statistics()
        print(f"4. 状态统计: 总表数={stats['total_tables']}, 转换次数={stats['transition_count']}")
        
        print("\n[成功] 分页状态管理测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 分页状态管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_validation():
    """测试数据验证增强"""
    print("\n" + "="*60)
    print("测试3: 数据验证增强")
    print("="*60)
    
    try:
        from src.modules.data_management.data_flow_validator import DataFlowValidator
        
        validator = DataFlowValidator()
        
        print("1. 测试正常数据...")
        normal_data = [
            {'工号': '001', '姓名': '张三', '部门': '技术部'},
            {'工号': '002', '姓名': '李四', '部门': '市场部'},
        ]
        normal_headers = ['工号', '姓名', '部门']
        
        result = validator.validate_data_consistency(
            data=normal_data,
            headers=normal_headers,
            table_type='test'
        )
        print(f"   正常数据验证: 有效={result.is_valid}, 问题数={len(result.issues)}")
        
        print("2. 测试异常表头（大量重复）...")
        bad_headers = ['工号'] * 20 + ['姓名'] * 20 + ['部门'] * 20
        result = validator.validate_data_consistency(
            data=normal_data,
            headers=bad_headers,
            table_type='test'
        )
        print(f"   异常表头验证: 有效={result.is_valid}")
        print(f"   检测到的问题: {result.issues[:2]}")  # 只显示前2个问题
        print(f"   应用的修复: {result.fixes_applied}")
        
        print("3. 测试表头数量异常...")
        huge_headers = [f'field_{i}' for i in range(150)]
        result = validator.validate_data_consistency(
            data=normal_data,
            headers=huge_headers,
            table_type='test'
        )
        print(f"   超大表头验证: 有效={result.is_valid}")
        print(f"   修复后表头数: {len(result.headers)}")
        
        print("4. 测试数据一致性...")
        inconsistent_data = [
            {'工号': '001', '姓名': '张三'},
            {'工号': '002', '姓名': '李四', '部门': '市场部', '额外字段': '值'},
        ]
        result = validator.validate_data_consistency(
            data=inconsistent_data,
            headers=['工号', '姓名', '部门'],
            table_type='test'
        )
        print(f"   不一致数据验证: 问题数={len(result.issues)}")
        
        print("\n[成功] 数据验证增强测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 数据验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_format_renderer_fix():
    """测试格式化渲染器修复"""
    print("\n" + "="*60)
    print("测试4: 格式化渲染器修复（P0修复）")
    print("="*60)
    
    try:
        from src.modules.format_management.format_renderer import FormatRenderer
        from src.modules.format_management.format_config import FormatConfig
        from src.modules.format_management.field_registry import FieldRegistry
        
        # 创建必要的实例
        config_path = 'config.json'
        mapping_path = 'state/data/field_mappings.json'
        format_config = FormatConfig(config_path)
        field_registry = FieldRegistry(mapping_path)
        renderer = FormatRenderer(format_config, field_registry)
        
        print("1. 测试字符串列格式化（map替代apply）...")
        test_series = pd.Series(['001', '002', '003', '004', '005'])
        config = {'empty_display': '', 'trim_whitespace': True, 'max_length': 100}
        
        start_time = time.time()
        result = renderer._render_string_column(test_series, config, '工号')
        elapsed = (time.time() - start_time) * 1000
        
        print(f"   格式化结果类型: {type(result)}")
        print(f"   格式化耗时: {elapsed:.2f}ms")
        print(f"   结果样本: {result.head(3).tolist()}")
        
        # 验证没有Series传递错误
        if isinstance(result, pd.Series) and len(result) == len(test_series):
            print("   [通过] 格式化正常，无Series传递错误")
        else:
            print("   [失败] 格式化异常")
            return False
        
        print("2. 测试数值列格式化...")
        numeric_series = pd.Series([1000.0, 2000.5, 3000.0, 4000.99, 5000.0])
        float_config = {'decimal_places': 2, 'thousand_separator': ',', 'zero_display': '0.00'}
        
        result = renderer._render_float_column(numeric_series, float_config, '工资')
        print(f"   数值格式化结果: {result.head(3).tolist()}")
        
        print("\n[成功] 格式化渲染器修复测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 格式化渲染器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def performance_test():
    """性能测试"""
    print("\n" + "="*60)
    print("测试5: 性能测试")
    print("="*60)
    
    try:
        from src.gui.prototype.widgets.table_header_cache import get_header_cache
        from src.gui.prototype.widgets.pagination_state_manager import get_pagination_manager
        
        cache = get_header_cache()
        manager = get_pagination_manager()
        
        print("1. 表头缓存性能...")
        headers = [f'column_{i}' for i in range(50)]
        
        # 首次缓存
        start = time.time()
        for i in range(100):
            cache.cache_headers(f'perf_table_{i}', headers)
        cache_time = (time.time() - start) * 1000
        print(f"   缓存100个表耗时: {cache_time:.2f}ms")
        
        # 缓存命中
        start = time.time()
        for i in range(100):
            cache.get_cached_headers(f'perf_table_{i}', headers)
        hit_time = (time.time() - start) * 1000
        print(f"   命中100次耗时: {hit_time:.2f}ms")
        
        print("2. 分页状态性能...")
        start = time.time()
        for i in range(100):
            manager.set_state(f'perf_table_{i}', 
                            current_page=i+1, 
                            total_records=1000)
        state_time = (time.time() - start) * 1000
        print(f"   设置100个状态耗时: {state_time:.2f}ms")
        
        print("3. 数据验证性能...")
        from src.modules.data_management.data_flow_validator import DataFlowValidator
        validator = DataFlowValidator()
        
        # 创建测试数据
        test_data = [
            {f'field_{j}': f'value_{i}_{j}' for j in range(30)}
            for i in range(1000)
        ]
        test_headers = [f'field_{j}' for j in range(30)]
        
        start = time.time()
        result = validator.validate_data_consistency(
            data=test_data,
            headers=test_headers,
            table_type='performance_test'
        )
        validation_time = (time.time() - start) * 1000
        print(f"   验证1000行数据耗时: {validation_time:.2f}ms")
        
        print("\n[成功] 性能测试完成")
        return True
        
    except Exception as e:
        print(f"[错误] 性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("\n" + "="*70)
    print("P0和P1级优化综合测试")
    print("="*70)
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 运行各项测试
    results.append(("表头缓存机制", test_header_cache()))
    results.append(("分页状态管理", test_pagination_state()))
    results.append(("数据验证增强", test_data_validation()))
    results.append(("格式化渲染器修复", test_format_renderer_fix()))
    results.append(("性能测试", performance_test()))
    
    # 测试总结
    print("\n" + "="*70)
    print("测试总结")
    print("="*70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "[PASS]" if result else "[FAIL]"
        print(f"{status} - {name}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n[成功] 所有测试通过！P0和P1级优化工作正常。")
        return True
    else:
        print(f"\n[警告] {total - passed} 个测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)