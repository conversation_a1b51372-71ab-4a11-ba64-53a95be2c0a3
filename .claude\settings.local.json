{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["<PERSON><PERSON>(python3:*)", "Bash(cp:*)", "<PERSON><PERSON>(python:*)", "Bash(rg:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(./../.venv/Scripts/python.exe -m py_compile:*)", "<PERSON><PERSON>(sed:*)", "Bash(E:projectcasesalary_changes.venvScriptsActivate.ps1)", "<PERSON><PERSON>(mv:*)", "Bash(../.venv/Scripts/python.exe temp/debug_field_types.py)", "Bash(../.venv/Scripts/python.exe temp/simple_test.py:*)", "Bash(ls:*)", "Bash(../.venv/Scripts/python.exe temp/test_change_data_fix.py)", "<PERSON><PERSON>(mkdir:*)", "Bash(../.venv/Scripts/python.exe temp/test_change_data_import.py)", "Bash(../.venv/Scripts/python.exe temp/check_change_tables.py)", "Bash(../.venv/Scripts/python.exe temp/test_format_renderer_fix.py)", "Bash(../.venv/Scripts/python.exe temp/fix_field_mappings.py)", "Bash(../.venv/Scripts/python.exe temp/test_p0_p1_comprehensive.py)", "Bash(../.venv/Scripts/python.exe temp/test_p2_comprehensive.py)", "Bash(../.venv/Scripts/python.exe temp/test_solution_a_header_fix.py)"], "deny": []}}