#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
P2级综合测试
测试统一数据流架构、错误恢复机制、性能优化等
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
import pandas as pd
import numpy as np
import time
import json

def test_unified_data_flow():
    """测试统一数据流架构"""
    print("\n" + "="*60)
    print("测试1: 统一数据流架构")
    print("="*60)
    
    try:
        from src.core.unified_data_flow import get_unified_data_flow
        
        # 获取数据流管理器
        data_flow = get_unified_data_flow()
        
        print("1. 测试正常数据流...")
        # 准备测试数据
        test_df = pd.DataFrame({
            '工号': ['001', '002', '003', '004', '005'],
            '姓名': ['张三', '李四', '王五', '赵六', '钱七'],
            '基本工资': [5000, 6000, 7000, 5500, 6500],
            '津贴': [1000, 1200, 1100, 1050, 1150],
            '补发': [500, 0, 300, 200, 100]
        })
        
        # 处理数据
        context = data_flow.process(
            data=test_df,
            table_name='test_table',
            table_type='salary',
            max_rows=1000
        )
        
        # 检查结果
        if context.errors:
            print(f"   [FAIL] 处理出错: {context.errors}")
            return False
        else:
            print(f"   [PASS] 数据处理成功")
            print(f"   - 输出形状: {context.data.shape if hasattr(context.data, 'shape') else 'N/A'}")
            print(f"   - 警告数: {len(context.warnings)}")
            print(f"   - 修复数: {len(context.metadata.get('fixes_applied', []))}")
        
        # 检查性能指标
        if context.metrics:
            total_time = sum(v for k, v in context.metrics.items() if k.endswith('_ms'))
            print(f"   - 总耗时: {total_time:.2f}ms")
            for stage, time_ms in context.metrics.items():
                if stage.endswith('_ms'):
                    print(f"     {stage}: {time_ms:.2f}ms")
        
        print("\n2. 测试异常数据处理...")
        # 包含异常的数据
        bad_df = pd.DataFrame({
            '工号': ['001', '002', '003'] * 50,  # 大量重复
            '姓名': ['张三', '李四', None] * 50,  # 包含空值
            '基本工资': [5000, 'invalid', 7000] * 50,  # 包含无效数据
            '津贴': [1000, 1200, np.nan] * 50  # 包含NaN
        })
        
        context2 = data_flow.process(
            data=bad_df,
            table_name='bad_table',
            table_type='salary'
        )
        
        if context2.warnings:
            print(f"   [PASS] 正确检测到问题: {len(context2.warnings)}个警告")
            for i, warning in enumerate(context2.warnings[:3]):  # 只显示前3个
                print(f"     警告{i+1}: {warning}")
        
        # 获取统计信息
        stats = data_flow.get_statistics()
        print(f"\n3. 数据流统计:")
        print(f"   - 总处理数: {stats['total_processed']}")
        print(f"   - 错误数: {stats['total_errors']}")
        print(f"   - 平均耗时: {stats['average_time_ms']:.2f}ms")
        print(f"   - 错误率: {stats['error_rate']:.2%}")
        
        print("\n[成功] 统一数据流架构测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 统一数据流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unified_format_manager():
    """测试统一格式管理器"""
    print("\n" + "="*60)
    print("测试2: 统一格式管理器")
    print("="*60)
    
    try:
        from src.modules.format_management.unified_format_manager import get_unified_format_manager
        
        # 获取格式管理器
        format_manager = get_unified_format_manager()
        
        print("1. 测试数据格式化...")
        test_df = pd.DataFrame({
            '工号': ['001', '002', '003'],
            '姓名': ['张三', '李四', '王五'],
            '2025年基础性绩效': [5000.123, 6000.456, 7000.789],
            '2025年奖励性绩效预发': [1000.111, 1200.222, 1100.333],
            '车补': [500.5, 600.6, 550.55]
        })
        
        # 格式化数据
        formatted_df = format_manager.format_data(
            data=test_df,
            table_type='active_employees'
        )
        
        if formatted_df is not None and not formatted_df.empty:
            print(f"   [PASS] 数据格式化成功: {formatted_df.shape}")
            
            # 检查数值格式化
            for col in ['2025年基础性绩效', '2025年奖励性绩效预发', '车补']:
                if col in formatted_df.columns:
                    # 检查是否为数值类型
                    if pd.api.types.is_numeric_dtype(formatted_df[col]):
                        print(f"   [PASS] {col} 已转换为数值类型")
                    else:
                        print(f"   [WARNING] {col} 未能转换为数值类型")
        else:
            print(f"   [FAIL] 格式化失败")
            return False
        
        print("\n2. 测试表头格式化...")
        raw_headers = ['employee_id', 'employee_name', 'basic_salary', 'allowance']
        formatted_headers = format_manager.format_headers(
            raw_headers=raw_headers,
            table_type='active_employees'
        )
        
        print(f"   原始表头: {raw_headers}")
        print(f"   格式化后: {formatted_headers}")
        
        if formatted_headers and len(formatted_headers) == len(raw_headers):
            print(f"   [PASS] 表头格式化成功")
        else:
            print(f"   [FAIL] 表头格式化失败")
        
        print("\n3. 测试缓存机制...")
        # 第一次调用
        start_time = time.time()
        result1 = format_manager.format_data(test_df, 'active_employees')
        time1 = (time.time() - start_time) * 1000
        
        # 第二次调用（应该使用缓存）
        start_time = time.time()
        result2 = format_manager.format_data(test_df, 'active_employees')
        time2 = (time.time() - start_time) * 1000
        
        print(f"   第一次调用: {time1:.2f}ms")
        print(f"   第二次调用: {time2:.2f}ms")
        
        if time2 < time1 * 0.5:  # 第二次应该快很多
            print(f"   [PASS] 缓存机制生效")
        else:
            print(f"   [INFO] 缓存效果不明显")
        
        # 获取统计信息
        stats = format_manager.get_stats()
        print(f"\n4. 格式管理器统计:")
        print(f"   - 总格式化数: {stats.get('total_formats', 0)}")
        print(f"   - 缓存命中数: {stats.get('cache_hits', 0)}")
        print(f"   - 缓存未命中数: {stats.get('cache_misses', 0)}")
        print(f"   - 缓存命中率: {stats.get('cache_hit_rate', 0):.1f}%")
        print(f"   - 错误数: {stats.get('errors', 0)}")
        
        print("\n[成功] 统一格式管理器测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 格式管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_recovery():
    """测试错误恢复机制"""
    print("\n" + "="*60)
    print("测试3: 错误恢复机制")
    print("="*60)
    
    try:
        from src.core.error_recovery_manager import get_error_recovery_manager
        
        recovery_manager = get_error_recovery_manager()
        
        print("1. 测试DataFrame.map错误恢复...")
        # 模拟DataFrame.map错误
        test_df = pd.DataFrame({
            '工号': ['001', '002'],
            '工资': [5000, 6000]
        })
        
        try:
            # 故意触发错误
            test_df.map(str)  # DataFrame没有map方法
        except AttributeError as e:
            # 检测错误
            error_context = recovery_manager.detect_error(e, {
                'component': 'test',
                'operation': 'map'
            })
            
            if error_context.error_type == 'dataframe_map_error':
                print(f"   [PASS] 正确检测到DataFrame.map错误")
                
                # 尝试恢复
                success, recovered = recovery_manager.attempt_recovery(error_context, test_df)
                if success:
                    print(f"   [PASS] 错误恢复成功")
                else:
                    print(f"   [INFO] 无法自动恢复")
            else:
                print(f"   [FAIL] 未能正确识别错误类型")
        
        print("\n2. 测试表头重复错误恢复...")
        # 模拟表头重复
        duplicate_headers = ['工号', '姓名', '工号', '姓名', '工号'] * 10
        
        # 检测错误
        error_context = recovery_manager.detect_error(
            Exception("表头重复"),
            {
                'component': 'header',
                'operation': 'validation',
                'headers': duplicate_headers
            }
        )
        
        # 尝试恢复
        success, recovered = recovery_manager.attempt_recovery(
            error_context,
            duplicate_headers
        )
        
        if success and isinstance(recovered, list):
            unique_count = len(set(recovered))
            print(f"   [PASS] 表头去重成功: {len(duplicate_headers)} -> {len(recovered)}个")
            print(f"   唯一表头数: {unique_count}")
        else:
            print(f"   [INFO] 表头恢复策略不适用")
        
        print("\n3. 测试错误统计...")
        stats = recovery_manager.get_error_statistics()
        print(f"   - 总错误数: {stats['total_errors']}")
        print(f"   - 恢复成功数: {stats.get('recovered_errors', stats.get('recovery_success', 0))}")
        print(f"   - 恢复失败数: {stats.get('failed_recoveries', stats.get('recovery_failed', 0))}")
        print(f"   - 恢复成功率: {stats.get('recovery_success_rate', 0):.0%}")
        
        print("\n[成功] 错误恢复机制测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 错误恢复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_optimization():
    """测试性能优化"""
    print("\n" + "="*60)
    print("测试4: 性能优化")
    print("="*60)
    
    try:
        print("1. 测试大数据集处理...")
        # 创建大数据集
        large_df = pd.DataFrame({
            '工号': [f'EMP{i:05d}' for i in range(5000)],
            '姓名': [f'员工{i}' for i in range(5000)],
            '基本工资': np.random.uniform(3000, 20000, 5000),
            '津贴': np.random.uniform(500, 3000, 5000),
            '补发': np.random.uniform(0, 1000, 5000)
        })
        
        from src.core.unified_data_flow import get_unified_data_flow
        data_flow = get_unified_data_flow()
        
        # 处理大数据集
        start_time = time.time()
        context = data_flow.process(
            data=large_df,
            table_name='large_table',
            table_type='salary'
        )
        elapsed = (time.time() - start_time) * 1000
        
        if not context.errors:
            print(f"   [PASS] 5000行数据处理成功")
            print(f"   - 耗时: {elapsed:.2f}ms")
            print(f"   - 平均每行: {elapsed/5000:.3f}ms")
            
            if elapsed < 5000:  # 小于5秒
                print(f"   [PASS] 性能良好（<5秒）")
            else:
                print(f"   [WARNING] 性能需要优化（>5秒）")
        else:
            print(f"   [FAIL] 处理失败: {context.errors}")
        
        print("\n2. 测试缓存性能...")
        from src.gui.prototype.widgets.table_header_cache import get_header_cache
        cache = get_header_cache()
        
        # 缓存大量表头
        for i in range(100):
            headers = [f'Column_{j}' for j in range(50)]
            cache.cache_headers(f'table_{i}', headers, headers)
        
        # 测试缓存查询性能
        start_time = time.time()
        for i in range(100):
            cached = cache.get_cached_headers(f'table_{i}')
        query_time = (time.time() - start_time) * 1000
        
        print(f"   100次缓存查询耗时: {query_time:.2f}ms")
        print(f"   平均每次: {query_time/100:.3f}ms")
        
        if query_time < 100:  # 小于100ms
            print(f"   [PASS] 缓存查询性能优秀")
        else:
            print(f"   [INFO] 缓存查询性能一般")
        
        # 获取缓存统计
        stats = cache.get_statistics()
        print(f"\n3. 缓存统计:")
        print(f"   - 缓存项数: {stats.get('total_cached', stats.get('total_tables', 0))}")
        print(f"   - 命中次数: {stats.get('cache_hits', 0)}")
        print(f"   - 未命中次数: {stats.get('cache_misses', 0)}")
        print(f"   - 命中率: {stats.get('hit_rate', 0):.1f}%")
        
        print("\n[成功] 性能优化测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("\n" + "="*70)
    print("P2级综合测试")
    print("="*70)
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 运行各项测试
    results.append(("统一数据流架构", test_unified_data_flow()))
    results.append(("统一格式管理器", test_unified_format_manager()))
    results.append(("错误恢复机制", test_error_recovery()))
    results.append(("性能优化", test_performance_optimization()))
    
    # 测试总结
    print("\n" + "="*70)
    print("测试总结")
    print("="*70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "[PASS]" if result else "[FAIL]"
        print(f"{status} - {name}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n[成功] 所有P2级综合测试通过！")
        print("\n架构优化成果:")
        print("1. 统一数据流 - 单向数据流架构，清晰的处理节点")
        print("2. 格式管理器 - 事件驱动，状态管理，缓存优化")
        print("3. 错误恢复 - 自动检测和恢复常见错误")
        print("4. 性能优化 - 大数据集处理，高效缓存机制")
        return True
    else:
        print(f"\n[警告] {total - passed} 个测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)