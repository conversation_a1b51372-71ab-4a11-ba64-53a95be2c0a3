#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
P0级问题修复测试脚本（简化版）
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_format_renderer():
    """测试格式化渲染器修复"""
    print("=" * 50)
    print("Testing Format Renderer Fix")
    print("=" * 50)
    
    try:
        import pandas as pd
        from src.modules.format_management.format_renderer import FormatRenderer
        from src.modules.format_management.format_config import FormatConfig
        from src.modules.format_management.field_registry import FieldRegistry
        
        # 创建测试实例
        renderer = FormatRenderer(FormatConfig(), FieldRegistry())
        
        # 创建测试数据
        test_series = pd.Series(['001', '002', '003'])
        print(f"Test data: {test_series.tolist()}")
        
        # 测试字符串列格式化
        config = {'empty_display': '', 'trim_whitespace': True, 'max_length': 100}
        result = renderer._render_string_column(test_series, config, 'test_field')
        
        if isinstance(result, pd.Series):
            print(f"Result: {result.tolist()}")
            print("[PASS] String column formatting works correctly")
            return True
        else:
            print("[FAIL] String column formatting failed")
            return False
            
    except Exception as e:
        print(f"[ERROR] Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_table_headers():
    """测试表头累积修复"""
    print("\n" + "=" * 50)
    print("Testing Table Header Fix")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
        
        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建表格
        table = VirtualizedExpandableTable()
        
        # 设置初始数据
        data1 = [{'id': '1', 'name': 'A'}, {'id': '2', 'name': 'B'}]
        headers = ['id', 'name']
        
        print("Setting initial data...")
        table.set_data(data1, headers)
        col_count_1 = table.columnCount()
        print(f"Initial column count: {col_count_1}")
        
        # 模拟分页
        table._pagination_mode = True
        
        print("Setting page 2 data...")
        data2 = [{'id': '3', 'name': 'C'}, {'id': '4', 'name': 'D'}]
        table.set_data(data2, headers)
        col_count_2 = table.columnCount()
        print(f"After pagination column count: {col_count_2}")
        
        if col_count_2 == col_count_1:
            print(f"[PASS] Column count stable at {col_count_2}")
            return True
        else:
            print(f"[FAIL] Column count changed from {col_count_1} to {col_count_2}")
            return False
            
    except Exception as e:
        print(f"[ERROR] Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("Starting P0 Fix Tests")
    print("=" * 70)
    
    test1 = test_format_renderer()
    test2 = test_table_headers()
    
    print("\n" + "=" * 70)
    print("Test Summary")
    print("=" * 70)
    
    if test1 and test2:
        print("[SUCCESS] All tests passed")
        return True
    else:
        print(f"[WARNING] Some tests failed - Test1: {test1}, Test2: {test2}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)