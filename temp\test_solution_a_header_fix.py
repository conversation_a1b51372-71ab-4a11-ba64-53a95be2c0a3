"""
方案A：表头累积问题修复测试脚本

测试要点：
1. 全局表头锁机制
2. 表头版本控制
3. 强制重置机制
4. 并发安全性
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QTableWidgetItem
from PyQt5.QtCore import Qt
from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.utils.log_config import setup_logger

logger = setup_logger(__name__)

class TestSolutionA:
    """方案A修复测试类"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        # 创建 ConfigSyncManager 实例
        config_sync_manager = ConfigSyncManager()
        # 提供 ConfigSyncManager 给表格组件
        self.table = VirtualizedExpandableTable(config_sync_manager=config_sync_manager)
        self.test_results = []
    
    def test_header_lock_mechanism(self):
        """测试1：全局表头锁机制"""
        logger.info("=" * 60)
        logger.info("测试1：全局表头锁机制")
        
        try:
            # 初始设置
            initial_count = 10
            self.table._safe_set_column_count(initial_count)
            
            # 并发测试
            def concurrent_update(count):
                """并发更新列数"""
                for i in range(5):
                    self.table._safe_set_column_count(count + i)
                    time.sleep(0.01)
            
            # 创建多个线程同时更新
            threads = []
            for i in range(3):
                t = threading.Thread(target=concurrent_update, args=(20 + i * 10,))
                threads.append(t)
                t.start()
            
            # 等待所有线程完成
            for t in threads:
                t.join()
            
            # 验证列数是否在合理范围内
            final_count = self.table.columnCount()
            if final_count <= 50:
                logger.info(f"✓ 表头锁测试通过：最终列数 {final_count} <= 50")
                self.test_results.append(("表头锁机制", "通过"))
            else:
                logger.error(f"✗ 表头锁测试失败：最终列数 {final_count} > 50")
                self.test_results.append(("表头锁机制", "失败"))
            
        except Exception as e:
            logger.error(f"表头锁测试异常：{e}")
            self.test_results.append(("表头锁机制", f"异常: {e}"))
    
    def test_version_control(self):
        """测试2：表头版本控制"""
        logger.info("=" * 60)
        logger.info("测试2：表头版本控制")
        
        try:
            # 记录初始版本
            initial_version = self.table._header_version
            
            # 相同列数的重复设置
            self.table._safe_set_column_count(30)
            version_after_first = self.table._header_version
            
            self.table._safe_set_column_count(30)
            version_after_second = self.table._header_version
            
            # 不同列数的设置
            self.table._safe_set_column_count(35)
            version_after_change = self.table._header_version
            
            # 验证版本控制
            if version_after_first > initial_version and \
               version_after_second == version_after_first and \
               version_after_change > version_after_second:
                logger.info(f"✓ 版本控制测试通过：版本递增正确 {initial_version} -> {version_after_first} -> {version_after_change}")
                self.test_results.append(("版本控制", "通过"))
            else:
                logger.error(f"✗ 版本控制测试失败：版本递增异常")
                self.test_results.append(("版本控制", "失败"))
            
        except Exception as e:
            logger.error(f"版本控制测试异常：{e}")
            self.test_results.append(("版本控制", f"异常: {e}"))
    
    def test_force_reset(self):
        """测试3：强制重置机制"""
        logger.info("=" * 60)
        logger.info("测试3：强制重置机制")
        
        try:
            # 模拟异常情况：设置过多列
            logger.info("模拟异常：尝试设置100列")
            self.table._safe_set_column_count(100)
            
            # 检查是否触发了重置
            current_count = self.table.columnCount()
            reset_count = self.table._header_reset_count
            
            if current_count <= 50 and reset_count > 0:
                logger.info(f"✓ 强制重置测试通过：列数 {current_count}，重置次数 {reset_count}")
                self.test_results.append(("强制重置", "通过"))
            else:
                logger.error(f"✗ 强制重置测试失败：列数 {current_count}，重置次数 {reset_count}")
                self.test_results.append(("强制重置", "失败"))
            
        except Exception as e:
            logger.error(f"强制重置测试异常：{e}")
            self.test_results.append(("强制重置", f"异常: {e}"))
    
    def test_header_accumulation_prevention(self):
        """测试4：防止表头累积"""
        logger.info("=" * 60)
        logger.info("测试4：防止表头累积")
        
        try:
            # 设置初始表头
            headers = [f"列{i}" for i in range(45)]
            self.table._safe_set_column_count(len(headers))
            self.table.setHorizontalHeaderLabels(headers)
            
            # 模拟多次分页操作
            logger.info("模拟10次分页操作...")
            for page in range(10):
                # 模拟分页时的表头更新
                self.table._safe_set_column_count(len(headers))
                time.sleep(0.05)  # 模拟处理延迟
            
            # 验证最终列数
            final_count = self.table.columnCount()
            if final_count == 45:
                logger.info(f"✓ 表头累积防止测试通过：最终列数 {final_count} = 45")
                self.test_results.append(("防止累积", "通过"))
            else:
                logger.error(f"✗ 表头累积防止测试失败：最终列数 {final_count} != 45")
                self.test_results.append(("防止累积", "失败"))
            
            # 检查表头内容
            actual_headers = []
            for col in range(self.table.columnCount()):
                item = self.table.horizontalHeaderItem(col)
                if item:
                    actual_headers.append(item.text())
            
            # 检查是否有重复
            unique_headers = list(set(actual_headers))
            if len(unique_headers) == len(actual_headers):
                logger.info(f"✓ 无重复表头：{len(unique_headers)}个唯一表头")
            else:
                logger.error(f"✗ 存在重复表头：{len(actual_headers)}个表头，{len(unique_headers)}个唯一")
            
        except Exception as e:
            logger.error(f"表头累积防止测试异常：{e}")
            self.test_results.append(("防止累积", f"异常: {e}"))
    
    def test_update_interval_control(self):
        """测试5：更新间隔控制"""
        logger.info("=" * 60)
        logger.info("测试5：更新间隔控制")
        
        try:
            # 快速连续更新
            start_version = self.table._header_version
            
            for i in range(10):
                self.table._safe_set_column_count(40 + i)
                # 不等待，快速连续调用
            
            end_version = self.table._header_version
            version_changes = end_version - start_version
            
            # 由于有间隔控制，版本变化应该少于10次
            if version_changes < 10:
                logger.info(f"✓ 更新间隔控制测试通过：10次调用，实际更新 {version_changes} 次")
                self.test_results.append(("间隔控制", "通过"))
            else:
                logger.error(f"✗ 更新间隔控制测试失败：10次调用，实际更新 {version_changes} 次")
                self.test_results.append(("间隔控制", "失败"))
            
        except Exception as e:
            logger.error(f"更新间隔控制测试异常：{e}")
            self.test_results.append(("间隔控制", f"异常: {e}"))
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始方案A修复测试")
        logger.info("=" * 60)
        
        # 运行各项测试
        self.test_header_lock_mechanism()
        self.test_version_control()
        self.test_force_reset()
        self.test_header_accumulation_prevention()
        self.test_update_interval_control()
        
        # 输出测试结果汇总
        logger.info("=" * 60)
        logger.info("测试结果汇总：")
        
        passed = 0
        failed = 0
        for test_name, result in self.test_results:
            if "通过" in result:
                passed += 1
                logger.info(f"  ✓ {test_name}: {result}")
            else:
                failed += 1
                logger.error(f"  ✗ {test_name}: {result}")
        
        logger.info(f"\n总计：{passed} 通过，{failed} 失败")
        
        if failed == 0:
            logger.info("\n🎉 方案A修复测试全部通过！")
        else:
            logger.warning(f"\n⚠️ 方案A修复测试有 {failed} 项未通过，需要进一步调试")
        
        return failed == 0

def main():
    """主测试入口"""
    try:
        tester = TestSolutionA()
        success = tester.run_all_tests()
        
        # 返回测试结果
        sys.exit(0 if success else 1)
        
    except Exception as e:
        logger.error(f"测试执行失败：{e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()