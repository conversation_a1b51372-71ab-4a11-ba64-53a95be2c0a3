{"version": "2.0", "last_updated": "2025-08-19T11:05:34.994443", "global_settings": {"auto_generate_mappings": true, "enable_smart_suggestions": true, "save_edit_history": true, "preserve_chinese_headers": true}, "table_mappings": {"salary_data_2025_07_active_employees": {"工号": "employee_id", "姓名": "employee_name", "部门名称": "department", "人员类别": "employee_type", "人员类别代码": "employee_type_code", "2025年岗位工资": "position_salary_2025", "2025年薪级工资": "grade_salary_2025", "津贴": "allowance", "结余津贴": "balance_allowance", "应发工资": "total_salary"}, "active_employees": {"工号": "employee_id", "姓名": "employee_name", "部门名称": "department", "人员类别": "employee_type", "人员类别代码": "employee_type_code"}}, "field_templates": {"离休人员工资表": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "basic_retirement_salary": "基本离休费", "balance_allowance": "结余津贴", "living_allowance": "生活补贴", "housing_allowance": "住房补贴", "property_allowance": "物业补贴", "retirement_allowance": "离休补贴", "nursing_fee": "护理费", "one_time_living_allowance": "增发一次性生活补贴", "supplement": "补发", "total": "合计", "advance": "借支", "remarks": "备注"}, "退休人员工资表": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "basic_retirement_salary": "基本退休费", "allowance": "津贴", "balance_allowance": "结余津贴", "retirement_living_allowance": "离退休生活补贴", "nursing_fee": "护理费", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "salary_advance": "增资预付", "adjustment_2016": "2016待遇调整", "adjustment_2017": "2017待遇调整", "adjustment_2018": "2018待遇调整", "adjustment_2019": "2019待遇调整", "adjustment_2020": "2020待遇调整", "adjustment_2021": "2021待遇调整", "adjustment_2022": "2022待遇调整", "adjustment_2023": "2023待遇调整", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund": "公积", "insurance_deduction": "保险扣款", "remarks": "备注"}, "全部在职人员工资表": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险"}, "A岗职工": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "seniority_salary_2025": "2025年校龄工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "living_allowance_2025": "2025年生活补贴", "car_allowance": "车补", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "insurance_deduction": "保险扣款", "pension_insurance": "代扣代存养老保险"}}, "user_preferences": {"default_field_patterns": {}, "recent_edits": [], "favorite_mappings": []}}