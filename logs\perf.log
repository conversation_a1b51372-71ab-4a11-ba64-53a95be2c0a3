2025-08-19 11:46:42.076 | INFO     | src.utils.log_config:_log_initialization_info:356 | 日志系统初始化完成
2025-08-19 11:46:42.076 | INFO     | src.utils.log_config:_log_initialization_info:357 | 日志级别: INFO
2025-08-19 11:46:42.077 | INFO     | src.utils.log_config:_log_initialization_info:358 | 控制台输出: True
2025-08-19 11:46:42.077 | INFO     | src.utils.log_config:_log_initialization_info:359 | 文件输出: True
2025-08-19 11:46:42.078 | INFO     | src.utils.log_config:_log_initialization_info:365 | 日志文件路径: logs/salary_system.log
2025-08-19 11:46:42.081 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-19 11:46:45.190 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-19 11:46:45.190 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-19 11:46:45.190 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-19 11:46:45.190 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-19 11:46:45.190 | INFO     | __main__:setup_app_logging:427 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-19 11:46:45.190 | INFO     | __main__:main:491 | 初始化核心管理器...
2025-08-19 11:46:45.190 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 11:46:45.190 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-19 11:46:45.190 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-19 11:46:45.190 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-19 11:46:45.190 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-19 11:46:45.219 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-19 11:46:45.219 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-19 11:46:45.219 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 11:46:45.235 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-19 11:46:45.235 | INFO     | __main__:main:496 | 核心管理器初始化完成。
2025-08-19 11:46:45.235 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-19 11:46:45.235 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-19 11:46:45.235 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 11:46:45.235 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-19 11:46:45.235 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-19 11:46:45.235 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-19 11:46:45.235 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-19 11:46:45.235 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11689 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-19 11:46:45.235 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 11:46:45.235 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11544 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-19 11:46:45.235 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11582 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-19 11:46:45.282 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-19 11:46:45.298 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-19 11:46:45.298 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-19 11:46:45.298 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:149 | 🔧 [配置修复] 创建了完整的默认配置，包含基本字段映射和模板
2025-08-19 11:46:45.316 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-19 11:46:45.316 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-19 11:46:45.321 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 11:46:45.321 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-19 11:46:45.322 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-19 11:46:45.322 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-19 11:46:45.322 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 11:46:45.322 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-19 11:46:45.322 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 24.3ms
2025-08-19 11:46:45.347 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-19 11:46:45.347 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-19 11:46:45.347 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-19 11:46:45.347 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-19 11:46:45.347 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-19 11:46:45.348 | INFO     | src.gui.prototype.prototype_main_window:__init__:3590 | 🚀 性能管理器已集成
2025-08-19 11:46:45.348 | INFO     | src.gui.prototype.prototype_main_window:__init__:3592 | ✅ 新架构集成成功！
2025-08-19 11:46:45.352 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3705 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-19 11:46:45.352 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3670 | ✅ 新架构事件监听器设置完成
2025-08-19 11:46:45.353 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-19 11:46:45.353 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-19 11:46:45.354 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-19 11:46:45.571 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2666 | 菜单栏创建完成
2025-08-19 11:46:45.571 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-19 11:46:45.571 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-19 11:46:45.571 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-19 11:46:45.571 | INFO     | src.gui.prototype.prototype_main_window:__init__:2641 | 菜单栏管理器初始化完成
2025-08-19 11:46:45.571 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-19 11:46:45.571 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5376 | 管理器设置完成，包含增强版表头管理器
2025-08-19 11:46:45.586 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5381 | 🔧 开始应用窗口级Material Design样式...
2025-08-19 11:46:45.586 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-19 11:46:45.586 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-19 11:46:45.586 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5388 | ✅ 窗口级样式应用成功
2025-08-19 11:46:45.586 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5429 | ✅ 响应式样式监听设置完成
2025-08-19 11:46:45.586 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-19 11:46:45.586 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-19 11:46:45.601 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-19 11:46:45.601 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-19 11:46:45.601 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-19 11:46:45.617 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1911 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-19 11:46:45.617 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1870 | 使用兜底数据加载导航
2025-08-19 11:46:45.617 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-19 11:46:45.617 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-19 11:46:45.617 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 0个展开项
2025-08-19 11:46:45.617 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-19 11:46:45.634 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-19 11:46:45.635 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-19 11:46:45.637 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-19 11:46:45.641 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1911 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-19 11:46:45.644 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1974 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-19 11:46:45.644 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-19 11:46:45.645 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-19 11:46:45.645 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-优化] 开始自动选择最新数据...
2025-08-19 11:46:45.646 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 11:46:45.647 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 11:46:45.649 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1403 | 🔧 [P1-优化] 暂未找到最新工资数据，可能需要先导入数据
2025-08-19 11:46:45.898 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-19 11:46:45.900 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-19 11:46:45.902 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-19 11:46:45.904 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-19 11:46:45.925 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-19 11:46:45.925 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-19 11:46:45.925 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-19 11:46:45.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2197 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-19 11:46:45.928 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-19 11:46:45.928 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-19 11:46:45.929 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-19 11:46:45.929 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2249 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-19 11:46:45.938 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-19 11:46:45.940 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-19 11:46:45.940 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2296 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-19 11:46:45.941 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-19 11:46:45.941 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-19 11:46:45.942 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: False
2025-08-19 11:46:45.943 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-19 11:46:45.944 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-19 11:46:45.946 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2303 | 列宽管理器初始化完成
2025-08-19 11:46:45.946 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2430 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-19 11:46:45.948 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2317 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-19 11:46:45.949 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 11:46:45.959 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 11:46:45.960 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 11:46:45.968 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-19 11:46:45.968 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7594 | 方案A：安全设置列数: 0
2025-08-19 11:46:45.973 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2840 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-19 11:46:45.974 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 11:46:45.981 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-19 11:46:45.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2892 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-19 11:46:45.989 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 11:46:46.004 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 11:46:46.005 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 11:46:46.006 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 11:46:46.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 11:46:46.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 11:46:46.011 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-19 11:46:46.019 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 50.8ms
2025-08-19 11:46:46.021 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 11:46:46.022 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 11:46:46.023 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-19 11:46:46.024 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 11:46:46.025 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 11:46:46.039 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-19 11:46:46.051 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-19 11:46:46.056 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-19 11:46:46.097 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-19 11:46:46.144 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-19 11:46:46.144 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-19 11:46:46.144 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5338 | 快捷键设置完成
2025-08-19 11:46:46.146 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5295 | 主窗口UI设置完成。
2025-08-19 11:46:46.148 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5532 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-19 11:46:46.149 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5564 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-19 11:46:46.150 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5576 | ✅ 已连接分页刷新信号到主窗口
2025-08-19 11:46:46.151 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5577 | ✅ 已连接分页组件事件到新架构
2025-08-19 11:46:46.152 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5588 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-19 11:46:46.152 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5591 | 信号连接设置完成
2025-08-19 11:46:46.153 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6849 | 🔧 [P1-2修复] 发现 2 个表的配置
2025-08-19 11:46:46.154 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-19 11:46:46.155 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-19 11:46:46.156 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6859 | ✅ [P1-2修复] 已加载字段映射信息，共2个表的映射
2025-08-19 11:46:46.164 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 11:46:46.165 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 11:46:46.166 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 11:46:46.166 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 11:46:46.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 11:46:46.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7594 | 方案A：安全设置列数: 22
2025-08-19 11:46:46.169 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 11:46:46.170 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 11:46:46.171 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 11:46:46.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 11:46:46.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 5.2ms
2025-08-19 11:46:46.176 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 11:46:46.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 11:46:46.178 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-19 11:46:46.179 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 11:46:46.179 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 11:46:46.181 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 11:46:46.181 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8420 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-19 11:46:46.183 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 11:46:46.184 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 11:46:46.184 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 11:46:46.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 11:46:46.190 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 11:46:46.190 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 11:46:46.191 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 11:46:46.193 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 11:46:46.196 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 11:46:46.197 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 7.8ms
2025-08-19 11:46:46.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 11:46:46.203 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 11:46:46.204 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1710 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-19 11:46:46.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 11:46:46.206 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 11:46:46.207 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8441 | 已显示标准空表格，表头数量: 22
2025-08-19 11:46:46.207 | INFO     | src.gui.prototype.prototype_main_window:__init__:3644 | 原型主窗口初始化完成
2025-08-19 11:46:46.248 | INFO     | __main__:main:518 | 应用程序启动成功
2025-08-19 11:46:46.268 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 11:46:46.354 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 11:46:46.356 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 11:46:46.429 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1468 | 执行延迟的自动选择最新数据...
2025-08-19 11:46:46.430 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-19 11:46:46.439 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2072 | MainWorkspaceArea 响应式适配: sm
2025-08-19 11:46:46.472 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 11:46:46.474 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 11:46:46.482 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1491 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-19 11:46:46.949 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9325 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-19 11:46:46.950 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9235 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-19 11:46:46.954 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9249 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-19 11:46:46.954 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9783 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-19 11:46:46.969 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9255 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-19 11:47:01.578 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 离休人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-19 11:47:01.578 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7292 | 导航变化: 工资表 > 2025年 > 05月 > 离休人员
2025-08-19 11:47:01.594 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10414 | 🚫 [用户要求] 表格切换不显示加载条:  -> None
2025-08-19 11:47:01.594 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10430 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 11:47:01.594 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8219 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '离休人员'] -> salary_data_2025_05_retired_employees
2025-08-19 11:47:01.594 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 11:47:01.594 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_retired_employees 的缓存
2025-08-19 11:47:01.594 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11100 | 已注册 2 个表格到表头管理器
2025-08-19 11:47:01.594 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-19 11:47:01.594 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-08-19 11:47:01.594 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-19 11:47:01.594 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7508 | 🆕 使用新架构加载数据: salary_data_2025_05_retired_employees（通过事件系统）
2025-08-19 11:47:01.594 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-19 11:47:01.594 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 11:47:01.594 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 11:47:01.594 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7517 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_retired_employees 尚未创建，显示空表格等待数据导入
2025-08-19 11:47:01.594 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8343 | 🔧 [数据流追踪] 使用离休人员表头: 15个字段
2025-08-19 11:47:01.594 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8416 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_retired_employees 的专用表头: 15个字段
2025-08-19 11:47:01.612 | INFO     | src.gui.prototype.prototype_main_window:set_data:845 | 空数据输入发生 3 次（2s 窗口），将显示空表提示
2025-08-19 11:47:01.612 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 11:47:01.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 11:47:01.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 11:47:01.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 11:47:01.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 11:47:01.625 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 11:47:01.625 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 11:47:01.625 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 11:47:01.625 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 11:47:01.625 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 11:47:01.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 16.3ms
2025-08-19 11:47:01.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 11:47:01.639 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 11:47:01.640 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 11:47:01.641 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 11:47:01.641 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 11:47:01.642 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 11:47:01.643 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8441 | 已显示标准空表格，表头数量: 15
2025-08-19 11:47:01.741 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 11:47:02.616 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-08-19 11:47:02.616 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7292 | 导航变化: 工资表 > 2025年 > 05月 > 退休人员
2025-08-19 11:47:02.616 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10414 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_retired_employees -> None
2025-08-19 11:47:02.616 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10430 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 11:47:02.616 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8219 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '退休人员'] -> salary_data_2025_05_pension_employees
2025-08-19 11:47:02.616 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 11:47:02.616 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_pension_employees 的缓存
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11100 | 已注册 2 个表格到表头管理器
2025-08-19 11:47:02.633 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-19 11:47:02.633 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-08-19 11:47:02.633 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7508 | 🆕 使用新架构加载数据: salary_data_2025_05_pension_employees（通过事件系统）
2025-08-19 11:47:02.633 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-19 11:47:02.633 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 11:47:02.633 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7517 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_pension_employees 尚未创建，显示空表格等待数据导入
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8355 | 🔧 [数据流追踪] 使用退休人员表头: 26个字段
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8416 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_pension_employees 的专用表头: 26个字段
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 11:47:02.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 11:47:02.647 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 14.3ms
2025-08-19 11:47:02.647 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 11:47:02.650 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 11:47:02.653 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 11:47:02.663 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 11:47:02.664 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 11:47:02.665 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 11:47:02.666 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8441 | 已显示标准空表格，表头数量: 26
2025-08-19 11:47:02.764 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 11:47:03.108 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月 > A岗职工', '工资表', '工资表 > 2025年']
2025-08-19 11:47:03.108 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7292 | 导航变化: 工资表 > 2025年 > 05月 > A岗职工
2025-08-19 11:47:03.108 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10414 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_pension_employees -> None
2025-08-19 11:47:03.108 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10430 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 11:47:03.108 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8219 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', 'A岗职工'] -> salary_data_2025_05_a_grade_employees
2025-08-19 11:47:03.108 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 11:47:03.108 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_a_grade_employees 的缓存
2025-08-19 11:47:03.108 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11100 | 已注册 2 个表格到表头管理器
2025-08-19 11:47:03.108 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7508 | 🆕 使用新架构加载数据: salary_data_2025_05_a_grade_employees（通过事件系统）
2025-08-19 11:47:03.108 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 11:47:03.108 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 11:47:03.108 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7517 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_a_grade_employees 尚未创建，显示空表格等待数据导入
2025-08-19 11:47:03.108 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8366 | 🔧 [数据流追踪] 使用A岗职工表头: 20个字段
2025-08-19 11:47:03.108 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8416 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_a_grade_employees 的专用表头: 20个字段
2025-08-19 11:47:03.108 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 11:47:03.108 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 11:47:03.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 11:47:03.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 11:47:03.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 11:47:03.123 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 11:47:03.123 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 11:47:03.123 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 11:47:03.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 11:47:03.133 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 9.7ms
2025-08-19 11:47:03.134 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 11:47:03.134 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 11:47:03.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 11:47:03.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 11:47:03.136 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 11:47:03.137 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8441 | 已显示标准空表格，表头数量: 20
2025-08-19 11:47:03.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 11:47:05.330 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2072 | MainWorkspaceArea 响应式适配: sm
2025-08-19 11:47:05.330 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-优化] 开始自动选择最新数据...
2025-08-19 11:47:05.330 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2068 | 开始获取最新工资数据路径...
2025-08-19 11:47:05.330 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2073 | 未找到任何工资数据表
2025-08-19 11:47:05.330 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1403 | 🔧 [P1-优化] 暂未找到最新工资数据，可能需要先导入数据
2025-08-19 11:47:06.221 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表']
2025-08-19 11:47:06.221 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7292 | 导航变化: 异动人员表
2025-08-19 11:47:06.221 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10414 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_a_grade_employees -> None
2025-08-19 11:47:06.221 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10430 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-19 11:47:06.221 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8092 | 🔧 [表名生成] 处理异动表路径: ['异动人员表']
2025-08-19 11:47:06.221 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8138 | 🔧 [P1修复] 异动表路径层次较少，使用当前年月: change_data_2025_08_active_employees
2025-08-19 11:47:06.221 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8140 | 🔧 [表名生成] 生成异动表名: change_data_2025_08_active_employees
2025-08-19 11:47:06.221 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 11:47:06.221 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 change_data_2025_08_active_employees 的缓存
2025-08-19 11:47:06.221 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11100 | 已注册 2 个表格到表头管理器
2025-08-19 11:47:06.221 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-19 11:47:06.221 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-08-19 11:47:06.221 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-19 11:47:06.221 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7508 | 🆕 使用新架构加载数据: change_data_2025_08_active_employees（通过事件系统）
2025-08-19 11:47:06.221 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-19 11:47:06.221 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-19 11:47:06.221 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1264 | 🔧 [深度修复] 找到 0 个匹配类型 'None' 的表 (尝试 1/5)
2025-08-19 11:47:06.221 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7517 | 🔧 [数据流追踪] 数据表 change_data_2025_08_active_employees 尚未创建，显示空表格等待数据导入
2025-08-19 11:47:06.237 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8333 | 🔧 [数据流追踪] 使用在职人员表头: 22个字段
2025-08-19 11:47:06.237 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8416 | 🔧 [数据流追踪] 使用表 change_data_2025_08_active_employees 的专用表头: 22个字段
2025-08-19 11:47:06.237 | INFO     | src.gui.prototype.prototype_main_window:set_data:845 | 空数据输入发生 3 次（2s 窗口），将显示空表提示
2025-08-19 11:47:06.237 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2299 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-19 11:47:06.237 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2738 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-19 11:47:06.237 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4570 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-19 11:47:06.237 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-19 11:47:06.237 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2976 | 表格格式化完成: default_table, 类型: active_employees
2025-08-19 11:47:06.237 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-19 11:47:06.237 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-19 11:47:06.237 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-19 11:47:06.237 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-19 11:47:06.237 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-19 11:47:06.252 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3160 | 表格数据设置完成: 0 行, 耗时: 15.4ms
2025-08-19 11:47:06.256 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8532 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-19 11:47:06.257 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8545 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-19 11:47:06.259 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-19 11:47:06.259 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3239 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-19 11:47:06.260 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2323 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-19 11:47:06.261 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-19 11:47:06.262 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8441 | 已显示标准空表格，表头数量: 22
2025-08-19 11:47:06.360 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8567 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-19 11:47:07.704 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-19 11:47:07.704 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5825 | 🔧 [P0-修复] 接收到数据导入请求，推断的目标路径: 异动人员表。打开导入对话框。
2025-08-19 11:47:07.704 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5834 | 🔧 [P0-修复] 开始创建数据导入对话框...
2025-08-19 11:47:07.719 | INFO     | src.gui.main_dialogs:__init__:55 | 🔧 [P0-修复] 开始初始化数据导入对话框...
2025-08-19 11:47:07.719 | INFO     | src.gui.main_dialogs:__init__:71 | 🔧 [P0-修复] 初始化导入器和验证器...
2025-08-19 11:47:07.719 | INFO     | src.gui.main_dialogs:__init__:76 | 🔧 [P0-修复] 初始化多Sheet导入器...
2025-08-19 11:47:07.719 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-19 11:47:07.719 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:75 | 多Sheet导入器初始化完成
2025-08-19 11:47:07.719 | INFO     | src.gui.main_dialogs:__init__:80 | 🔧 [P0-修复] 初始化默认设置管理器...
2025-08-19 11:47:07.719 | INFO     | src.gui.main_dialogs:__init__:85 | 🔧 [P0-修复] 开始初始化用户界面...
2025-08-19 11:47:07.719 | INFO     | src.gui.main_dialogs:_init_ui:110 | 🔧 [P0-修复] 设置对话框基本属性...
2025-08-19 11:47:07.719 | INFO     | src.gui.main_dialogs:_init_ui:120 | 🔧 [P0-修复] 创建主布局...
2025-08-19 11:47:07.719 | INFO     | src.gui.main_dialogs:_init_ui:125 | 🔧 [P0-修复] 创建目标选择组件...
2025-08-19 11:47:07.719 | INFO     | src.gui.widgets.target_selection_widget:__init__:112 | 🔧 [P0-修复] 开始初始化目标选择组件...
2025-08-19 11:47:07.719 | INFO     | src.gui.widgets.target_selection_widget:__init__:128 | 🔧 [P0-修复] 加载可选项配置...
2025-08-19 11:47:07.719 | INFO     | src.gui.widgets.target_selection_widget:_save_config:307 | 配置保存成功
2025-08-19 11:47:07.719 | INFO     | src.gui.widgets.target_selection_widget:__init__:132 | 🔧 [P0-修复] 初始化目标选择组件UI...
2025-08-19 11:47:07.735 | INFO     | src.gui.widgets.target_selection_widget:__init__:136 | 🔧 [P0-修复] 加载初始目标...
2025-08-19 11:47:07.735 | INFO     | src.gui.widgets.target_selection_widget:__init__:139 | 🔧 [P0-修复] 目标选择组件初始化完成
2025-08-19 11:47:07.735 | INFO     | src.gui.main_dialogs:_init_ui:130 | 🔧 [P0-修复] 设置目标路径: 异动人员表
2025-08-19 11:47:07.751 | ERROR    | src.gui.main_dialogs:__init__:101 | 🔧 [P0-修复] 数据导入对话框初始化失败: 'QComboBox' object has no attribute 'currentDataChanged'
2025-08-19 11:47:12.475 | ERROR    | src.gui.prototype.prototype_main_window:_on_import_data_requested:5875 | 🔧 [P0-修复] 打开数据导入对话框失败: 'QComboBox' object has no attribute 'currentDataChanged'
2025-08-19 11:47:19.617 | INFO     | __main__:main:523 | 应用程序正常退出
2025-08-19 11:47:19.648 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_0_2423101491968 已自动清理（弱引用回调）
