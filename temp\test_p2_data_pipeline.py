#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
P2级数据管道架构测试
测试统一数据处理管道的功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
import pandas as pd
import time
import numpy as np
from typing import List, Dict, Any

def test_unified_pipeline():
    """测试统一数据管道"""
    print("\n" + "="*60)
    print("测试1: 统一数据管道基础功能")
    print("="*60)
    
    try:
        from src.core.data_pipeline import get_data_pipeline, PipelineStage
        
        pipeline = get_data_pipeline()
        
        print("1. 测试正常数据处理...")
        # 创建测试数据
        test_data = pd.DataFrame({
            '工号': ['001', '002', '003', '004', '005'],
            '姓名': ['张三', '李四', '王五', '赵六', '钱七'],
            '基本工资': [5000, 6000, 5500, 7000, 6500],
            '津贴': [1000, 1200, 1100, 1400, 1300],
            '合计': [6000, 7200, 6600, 8400, 7800]
        })
        
        headers = list(test_data.columns)
        
        # 执行管道处理
        context = pipeline.process(
            data=test_data,
            headers=headers,
            table_name='test_table',
            table_type='salary'
        )
        
        print(f"   处理阶段: {context.stage.value}")
        print(f"   错误数: {len(context.errors)}")
        print(f"   警告数: {len(context.warnings)}")
        print(f"   性能指标: {list(context.performance_metrics.keys())}")
        
        if context.errors:
            print(f"   错误信息: {context.errors[:2]}")
        
        # 验证数据
        if context.data is not None:
            print(f"   输出数据形状: {context.data.shape}")
            print("[PASS] 正常数据处理成功")
        else:
            print("[FAIL] 数据处理失败")
            return False
            
        print("\n2. 测试缓存功能...")
        # 再次处理相同数据，应该命中缓存
        context2 = pipeline.process(
            data=test_data,
            headers=headers,
            table_name='test_table',
            table_type='salary'
        )
        
        if context2.metadata.get('cache_hit'):
            print("   [PASS] 缓存命中")
        else:
            print("   [INFO] 首次处理，缓存未命中")
        
        # 获取统计信息
        stats = pipeline.get_statistics()
        print(f"\n3. 管道统计:")
        print(f"   总处理次数: {stats['total_processed']}")
        print(f"   错误次数: {stats['error_count']}")
        print(f"   错误率: {stats['error_rate']:.2%}")
        if 'cache_stats' in stats and stats['cache_stats']:
            cache_stats = stats['cache_stats']
            print(f"   缓存大小: {cache_stats.get('cache_size', 0)}")
            print(f"   缓存命中率: {cache_stats.get('hit_rate', 0):.2%}")
        
        print("\n[成功] 统一数据管道测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 统一数据管道测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pipeline_validation():
    """测试管道验证功能"""
    print("\n" + "="*60)
    print("测试2: 管道数据验证")
    print("="*60)
    
    try:
        from src.core.data_pipeline import get_data_pipeline
        
        pipeline = get_data_pipeline()
        
        print("1. 测试异常表头检测...")
        # 创建异常表头数据
        bad_headers = ['工号'] * 30 + ['姓名'] * 30 + ['部门'] * 30
        test_data = [
            {'工号': f'00{i}', '姓名': f'员工{i}', '部门': '技术部'}
            for i in range(5)
        ]
        
        context = pipeline.process(
            data=test_data,
            headers=bad_headers,
            table_name='bad_table',
            table_type='test'
        )
        
        print(f"   警告数: {len(context.warnings)}")
        if context.warnings:
            print(f"   警告示例: {context.warnings[0]}")
        
        print("\n2. 测试数据类型转换...")
        # 混合类型数据
        mixed_data = pd.DataFrame({
            '工号': ['001', '002', '003'],
            '姓名': ['张三', '李四', '王五'],
            '基本工资': ['5000', '6000.5', 'N/A'],  # 字符串数值
            '津贴': [1000, 1200, 1100]  # 正常数值
        })
        
        context = pipeline.process(
            data=mixed_data,
            headers=list(mixed_data.columns),
            table_name='mixed_table',
            table_type='salary'
        )
        
        if context.data is not None:
            # 检查数据类型转换
            salary_col = context.data['基本工资']
            print(f"   工资列类型: {salary_col.dtype}")
            print(f"   工资列值: {salary_col.tolist()}")
            
            if pd.api.types.is_numeric_dtype(salary_col):
                print("   [PASS] 数值类型转换成功")
            else:
                print("   [INFO] 数值类型保持原样")
        
        print("\n[成功] 管道验证测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 管道验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pipeline_performance():
    """测试管道性能"""
    print("\n" + "="*60)
    print("测试3: 管道性能测试")
    print("="*60)
    
    try:
        from src.core.data_pipeline import get_data_pipeline
        
        pipeline = get_data_pipeline()
        
        print("1. 大数据集处理性能...")
        # 创建大数据集
        rows = 5000
        cols = 50
        
        large_data = pd.DataFrame(
            np.random.randn(rows, cols),
            columns=[f'col_{i}' for i in range(cols)]
        )
        
        # 添加一些需要格式化的列
        large_data['工号'] = [f'{i:06d}' for i in range(rows)]
        large_data['姓名'] = [f'员工{i}' for i in range(rows)]
        large_data['工资'] = np.random.uniform(3000, 20000, rows)
        
        headers = list(large_data.columns)
        
        start_time = time.time()
        context = pipeline.process(
            data=large_data,
            headers=headers,
            table_name='large_table',
            table_type='performance_test'
        )
        total_time = (time.time() - start_time) * 1000
        
        print(f"   数据规模: {rows}行 x {cols + 3}列")
        print(f"   总处理时间: {total_time:.2f}ms")
        print(f"   平均每行: {total_time/rows:.4f}ms")
        
        # 显示各阶段耗时
        if context.performance_metrics:
            print("\n   各阶段耗时:")
            for stage, time_ms in context.performance_metrics.items():
                if stage.endswith('_ms'):
                    print(f"     - {stage}: {time_ms:.2f}ms")
        
        print("\n2. 缓存效率测试...")
        # 多次处理测试缓存
        cache_times = []
        for i in range(5):
            start = time.time()
            ctx = pipeline.process(
                data=large_data[:100],  # 使用部分数据
                headers=headers,
                table_name=f'cache_test_{i % 2}',  # 交替使用两个表名
                table_type='cache_test'
            )
            elapsed = (time.time() - start) * 1000
            cache_times.append(elapsed)
            
        print(f"   处理时间: {cache_times}")
        print(f"   平均时间: {np.mean(cache_times):.2f}ms")
        
        # 检查缓存统计
        stats = pipeline.get_statistics()
        if 'cache_stats' in stats and stats['cache_stats']:
            cache_stats = stats['cache_stats']
            print(f"   缓存命中率: {cache_stats.get('hit_rate', 0):.2%}")
        
        print("\n[成功] 性能测试完成")
        return True
        
    except Exception as e:
        print(f"[错误] 性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pipeline_error_handling():
    """测试错误处理"""
    print("\n" + "="*60)
    print("测试4: 错误处理和恢复")
    print("="*60)
    
    try:
        from src.core.data_pipeline import get_data_pipeline
        
        pipeline = get_data_pipeline()
        
        print("1. 测试空数据处理...")
        context = pipeline.process(
            data=None,
            headers=[],
            table_name='empty_table',
            table_type='test'
        )
        
        print(f"   处理结果: 阶段={context.stage.value}")
        print(f"   错误数: {len(context.errors)}")
        
        print("\n2. 测试无效数据格式...")
        invalid_data = "This is not valid data"
        context = pipeline.process(
            data=invalid_data,
            headers=['col1', 'col2'],
            table_name='invalid_table',
            table_type='test'
        )
        
        print(f"   错误数: {len(context.errors)}")
        if context.errors:
            print(f"   错误信息: {context.errors[0]}")
        
        print("\n3. 测试极端情况...")
        # 超大列数
        huge_headers = [f'field_{i}' for i in range(500)]
        small_data = [{'field_0': 'value'}]
        
        context = pipeline.process(
            data=small_data,
            headers=huge_headers,
            table_name='extreme_table',
            table_type='test'
        )
        
        print(f"   原始列数: {len(huge_headers)}")
        if context.data is not None:
            print(f"   处理后列数: {len(context.data.columns)}")
        print(f"   警告数: {len(context.warnings)}")
        
        # 检查错误恢复
        stats = pipeline.get_statistics()
        print(f"\n4. 错误统计:")
        print(f"   总处理数: {stats['total_processed']}")
        print(f"   错误数: {stats['error_count']}")
        print(f"   错误率: {stats['error_rate']:.2%}")
        
        print("\n[成功] 错误处理测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 错误处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pipeline_context():
    """测试管道上下文功能"""
    print("\n" + "="*60)
    print("测试5: 管道上下文管理")
    print("="*60)
    
    try:
        from src.core.data_pipeline import PipelineContext, PipelineStage
        
        print("1. 测试上下文创建...")
        context = PipelineContext(
            table_name='test_table',
            table_type='salary'
        )
        
        print(f"   初始阶段: {context.stage.value}")
        print(f"   缓存键: {context.generate_cache_key()}")
        
        print("\n2. 测试指标记录...")
        context.add_metric('test_metric', 123.45)
        context.add_metric('another_metric', 67.89)
        
        print(f"   记录的指标: {list(context.performance_metrics.keys())}")
        print(f"   指标值: {list(context.performance_metrics.values())}")
        
        print("\n3. 测试错误和警告...")
        context.add_warning("这是一个测试警告")
        context.add_error("这是一个测试错误")
        
        print(f"   警告数: {len(context.warnings)}")
        print(f"   错误数: {len(context.errors)}")
        
        print("\n4. 测试元数据...")
        context.metadata['custom_field'] = 'custom_value'
        context.metadata['processing_time'] = 100.0
        
        print(f"   元数据字段: {list(context.metadata.keys())}")
        
        print("\n[成功] 上下文管理测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 上下文管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("\n" + "="*70)
    print("P2级数据管道架构测试")
    print("="*70)
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 运行各项测试
    results.append(("统一数据管道", test_unified_pipeline()))
    results.append(("管道验证功能", test_pipeline_validation()))
    results.append(("管道性能", test_pipeline_performance()))
    results.append(("错误处理", test_pipeline_error_handling()))
    results.append(("上下文管理", test_pipeline_context()))
    
    # 测试总结
    print("\n" + "="*70)
    print("测试总结")
    print("="*70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "[PASS]" if result else "[FAIL]"
        print(f"{status} - {name}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n[成功] 所有P2级数据管道测试通过！")
        return True
    else:
        print(f"\n[警告] {total - passed} 个测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)