#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
P2级架构集成测试
测试新架构与现有系统的集成
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
import pandas as pd
import time
import numpy as np
from typing import List, Dict, Any

def test_error_recovery():
    """测试错误恢复机制"""
    print("\n" + "="*60)
    print("测试1: 错误恢复机制")
    print("="*60)
    
    try:
        from src.core.error_recovery_manager import get_error_recovery_manager, ErrorSeverity
        
        manager = get_error_recovery_manager()
        
        print("1. 测试表头重复错误恢复...")
        # 模拟表头重复错误
        duplicate_headers = ['工号', '工号', '姓名', '姓名', '姓名', '部门']
        
        try:
            # 模拟错误
            raise ValueError("检测到严重表头重复")
        except Exception as e:
            context = manager.detect_error(e, {
                'component': 'table_renderer',
                'operation': 'render_headers',
                'data_info': {'headers': duplicate_headers}
            })
            
            print(f"   错误类型: {context.error_type}")
            print(f"   严重性: {context.severity.value}")
            print(f"   恢复策略: {context.recovery_strategy.value}")
            
            # 尝试恢复
            success, fixed_headers = manager.attempt_recovery(context, duplicate_headers)
            if success:
                print(f"   [PASS] 恢复成功: {fixed_headers}")
            else:
                print(f"   [FAIL] 恢复失败")
                return False
        
        print("\n2. 测试Series传递错误恢复...")
        test_series = pd.Series([1, 2, 3])
        
        try:
            # 模拟Series错误
            raise TypeError("_render_string_value错误地接收到Series")
        except Exception as e:
            context = manager.detect_error(e, {
                'component': 'format_renderer',
                'operation': 'format_column'
            })
            
            success, fixed_data = manager.attempt_recovery(context, test_series)
            print(f"   恢复结果: {success}, 数据类型: {type(fixed_data)}")
        
        print("\n3. 测试内存溢出降级...")
        large_df = pd.DataFrame(np.random.randn(20000, 100))
        
        try:
            raise MemoryError("内存不足")
        except Exception as e:
            context = manager.detect_error(e, {
                'component': 'data_processor',
                'operation': 'process_large_data'
            })
            
            print(f"   原始数据大小: {large_df.shape}")
            success, reduced_data = manager.attempt_recovery(context, large_df)
            if success and isinstance(reduced_data, pd.DataFrame):
                print(f"   降级后大小: {reduced_data.shape}")
                print("   [PASS] 数据降级成功")
            else:
                print("   [FAIL] 数据降级失败")
        
        # 获取统计
        stats = manager.get_error_statistics()
        print(f"\n4. 错误统计:")
        print(f"   总错误数: {stats['total_errors']}")
        print(f"   按严重性: {stats['by_severity']}")
        print(f"   按类型: {stats['by_type']}")
        print(f"   恢复成功率: {stats['recovery_success_rate']:.2%}")
        
        print("\n[成功] 错误恢复机制测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 错误恢复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pipeline_with_error_recovery():
    """测试管道与错误恢复的集成"""
    print("\n" + "="*60)
    print("测试2: 管道与错误恢复集成")
    print("="*60)
    
    try:
        from src.core.data_pipeline import get_data_pipeline
        from src.core.error_recovery_manager import get_error_recovery_manager
        
        pipeline = get_data_pipeline()
        error_manager = get_error_recovery_manager()
        
        print("1. 测试带异常数据的管道处理...")
        
        # 创建有问题的数据
        problematic_data = pd.DataFrame({
            '工号': ['001', '002', None, '004'],
            '姓名': ['张三', '李四', '王五', None],
            '工资': ['5000', 'invalid', '6000', '7000'],  # 含无效数值
            '部门': ['技术', '技术', '技术', '技术']
        })
        
        # 创建重复表头
        duplicate_headers = ['工号', '工号', '姓名', '工资', '工资', '工资', '部门']
        
        # 处理数据
        context = pipeline.process(
            data=problematic_data,
            headers=duplicate_headers,
            table_name='problematic_table',
            table_type='test'
        )
        
        print(f"   处理结果: 阶段={context.stage.value}")
        print(f"   错误数: {len(context.errors)}")
        print(f"   警告数: {len(context.warnings)}")
        
        if context.warnings:
            print(f"   警告示例: {context.warnings[0]}")
        
        if context.data is not None:
            print(f"   输出数据形状: {context.data.shape}")
            print("   [PASS] 异常数据处理成功")
        else:
            print("   [FAIL] 异常数据处理失败")
            return False
        
        print("\n2. 测试错误恢复统计...")
        stats = error_manager.get_error_statistics()
        if stats['total_errors'] > 0:
            print(f"   检测到的错误: {stats['total_errors']}")
            print(f"   恢复成功率: {stats['recovery_success_rate']:.2%}")
        else:
            print("   无错误记录")
        
        print("\n[成功] 管道与错误恢复集成测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_integration():
    """测试完整P2架构集成"""
    print("\n" + "="*60)
    print("测试3: 完整P2架构集成")
    print("="*60)
    
    try:
        # 导入所有P2组件
        from src.core.data_pipeline import get_data_pipeline
        from src.core.error_recovery_manager import get_error_recovery_manager
        from src.gui.prototype.widgets.table_header_cache import get_header_cache
        from src.gui.prototype.widgets.pagination_state_manager import get_pagination_manager
        
        print("1. 初始化所有P2组件...")
        pipeline = get_data_pipeline()
        error_manager = get_error_recovery_manager()
        header_cache = get_header_cache()
        pagination_manager = get_pagination_manager()
        
        print("   [PASS] 所有组件初始化成功")
        
        print("\n2. 模拟实际工作流...")
        
        # 模拟数据导入
        test_data = pd.DataFrame({
            '工号': [f'{i:04d}' for i in range(100)],
            '姓名': [f'员工{i}' for i in range(100)],
            '部门': ['技术部'] * 30 + ['市场部'] * 30 + ['财务部'] * 40,
            '基本工资': np.random.uniform(5000, 15000, 100),
            '津贴': np.random.uniform(1000, 3000, 100)
        })
        
        # 通过管道处理
        print("   处理数据...")
        context = pipeline.process(
            data=test_data,
            headers=list(test_data.columns),
            table_name='salary_table',
            table_type='salary'
        )
        
        if context.errors:
            print(f"   有{len(context.errors)}个错误")
        else:
            print("   数据处理成功")
        
        # 缓存表头
        print("   缓存表头...")
        header_cache.cache_headers(
            'salary_table',
            list(test_data.columns),
            list(test_data.columns)
        )
        
        # 设置分页状态
        print("   设置分页...")
        pagination_manager.set_state(
            'salary_table',
            current_page=1,
            page_size=50,
            total_records=len(test_data)
        )
        
        # 获取统计
        pipeline_stats = pipeline.get_statistics()
        error_stats = error_manager.get_error_statistics()
        cache_stats = header_cache.get_statistics()
        pagination_stats = pagination_manager.get_statistics()
        
        print("\n3. 集成统计:")
        print(f"   管道处理: {pipeline_stats['total_processed']}次")
        print(f"   错误恢复: {error_stats['total_errors']}个错误")
        print(f"   表头缓存: {cache_stats['total_entries']}个条目")
        print(f"   分页管理: {pagination_stats['total_tables']}个表")
        
        print("\n[成功] 完整P2架构集成测试通过")
        return True
        
    except Exception as e:
        print(f"[错误] 完整集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_with_p2():
    """测试P2优化后的性能"""
    print("\n" + "="*60)
    print("测试4: P2优化性能测试")
    print("="*60)
    
    try:
        from src.core.data_pipeline import get_data_pipeline
        
        pipeline = get_data_pipeline()
        
        print("1. 基准性能测试...")
        
        # 不同规模的数据
        sizes = [100, 500, 1000, 5000]
        times = []
        
        for size in sizes:
            # 创建测试数据
            test_data = pd.DataFrame({
                f'col_{i}': np.random.randn(size)
                for i in range(20)
            })
            
            # 测试处理时间
            start = time.time()
            context = pipeline.process(
                data=test_data,
                headers=list(test_data.columns),
                table_name=f'perf_test_{size}',
                table_type='performance'
            )
            elapsed = (time.time() - start) * 1000
            times.append(elapsed)
            
            print(f"   {size}行数据: {elapsed:.2f}ms")
        
        # 计算性能指标
        if len(times) > 1 and times[0] > 0:
            # 线性度检查
            ratios = [times[i]/times[0] if times[0] > 0 else 1 for i in range(len(times))]
            size_ratios = [sizes[i]/sizes[0] for i in range(len(sizes))]
            
            print("\n2. 性能分析:")
            print("   数据规模比: ", size_ratios)
            print("   时间比: ", [f"{r:.2f}" for r in ratios])
            
            # 检查是否接近线性
            avg_efficiency = sum(size_ratios[i]/ratios[i] for i in range(1, len(ratios))) / (len(ratios) - 1)
            print(f"   平均效率: {avg_efficiency:.2%}")
            
            if avg_efficiency > 0.7:
                print("   [PASS] 性能扩展性良好")
            else:
                print("   [INFO] 性能扩展性一般")
        
        print("\n3. 缓存效果测试...")
        
        # 重复处理相同数据
        test_data = pd.DataFrame(np.random.randn(1000, 20))
        headers = [f'col_{i}' for i in range(20)]
        
        times_with_cache = []
        for i in range(5):
            start = time.time()
            context = pipeline.process(
                data=test_data,
                headers=headers,
                table_name='cache_test',
                table_type='cache_performance'
            )
            elapsed = (time.time() - start) * 1000
            times_with_cache.append(elapsed)
        
        print(f"   处理时间: {times_with_cache}")
        
        # 检查缓存加速
        if len(times_with_cache) > 1:
            first_time = times_with_cache[0]
            avg_cached = sum(times_with_cache[1:]) / (len(times_with_cache) - 1)
            speedup = first_time / avg_cached if avg_cached > 0 else 1
            
            print(f"   首次处理: {first_time:.2f}ms")
            print(f"   缓存后平均: {avg_cached:.2f}ms")
            print(f"   加速比: {speedup:.2f}x")
            
            if speedup > 1.5:
                print("   [PASS] 缓存效果显著")
            else:
                print("   [INFO] 缓存效果一般")
        
        print("\n[成功] P2优化性能测试完成")
        return True
        
    except Exception as e:
        print(f"[错误] 性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("\n" + "="*70)
    print("P2级架构集成测试")
    print("="*70)
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 运行各项测试
    results.append(("错误恢复机制", test_error_recovery()))
    results.append(("管道与错误恢复集成", test_pipeline_with_error_recovery()))
    results.append(("完整P2架构集成", test_full_integration()))
    results.append(("P2优化性能", test_performance_with_p2()))
    
    # 测试总结
    print("\n" + "="*70)
    print("测试总结")
    print("="*70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "[PASS]" if result else "[FAIL]"
        print(f"{status} - {name}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n[成功] 所有P2级集成测试通过！架构优化完成。")
        print("\nP2级优化成果总结:")
        print("1. 统一数据处理管道 - 提供一致的数据流")
        print("2. 自动错误恢复机制 - 提高系统稳定性")
        print("3. 智能缓存策略 - 改善响应速度")
        print("4. 降级处理方案 - 确保系统可用性")
        return True
    else:
        print(f"\n[警告] {total - passed} 个测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)