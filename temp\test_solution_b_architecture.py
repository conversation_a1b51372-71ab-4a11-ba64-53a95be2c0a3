"""
方案B：架构重构测试脚本

测试要点：
1. UnifiedHeaderManager单例模式
2. 状态机管理
3. 分页模式特殊处理
4. 缓存机制
5. 表头累积防止
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QTableWidget
from src.gui.prototype.widgets.unified_header_manager import (
    get_header_manager,
    TableState
)
from src.gui.prototype.widgets.table_header_adapter import (
    apply_header_management,
    remove_header_management
)
from src.utils.log_config import setup_logger

logger = setup_logger(__name__)


class TestSolutionB:
    """方案B架构测试类"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.table = QTableWidget()
        self.header_manager = get_header_manager()
        self.adapter = None
        self.test_results = []
    
    def setup(self):
        """设置测试环境"""
        # 应用表头管理
        self.adapter = apply_header_management(self.table)
        logger.info("测试环境设置完成")
    
    def teardown(self):
        """清理测试环境"""
        if self.adapter:
            remove_header_management(self.table)
        logger.info("测试环境清理完成")
    
    def test_singleton_pattern(self):
        """测试1：单例模式"""
        logger.info("=" * 60)
        logger.info("测试1：单例模式")
        
        try:
            # 获取多个实例
            manager1 = get_header_manager()
            manager2 = get_header_manager()
            
            # 验证是同一个实例
            if manager1 is manager2:
                logger.info("✓ 单例模式测试通过：多次获取返回同一实例")
                self.test_results.append(("单例模式", "通过"))
            else:
                logger.error("✗ 单例模式测试失败：返回了不同实例")
                self.test_results.append(("单例模式", "失败"))
                
        except Exception as e:
            logger.error(f"单例模式测试异常：{e}")
            self.test_results.append(("单例模式", f"异常: {e}"))
    
    def test_state_machine(self):
        """测试2：状态机管理"""
        logger.info("=" * 60)
        logger.info("测试2：状态机管理")
        
        try:
            state_manager = self.header_manager.state_manager
            
            # 测试状态转换
            initial_state = state_manager.get_state()
            
            # IDLE -> LOADING
            success1 = state_manager.transition_to(TableState.LOADING)
            
            # LOADING -> IDLE
            success2 = state_manager.transition_to(TableState.IDLE)
            
            # IDLE -> PAGINATION
            success3 = state_manager.transition_to(TableState.PAGINATION)
            
            # PAGINATION -> IDLE
            success4 = state_manager.transition_to(TableState.IDLE)
            
            # 无效转换：IDLE -> ERROR（不在允许列表中）
            success5 = state_manager.transition_to(TableState.ERROR)
            
            if success1 and success2 and success3 and success4 and not success5:
                logger.info("✓ 状态机测试通过：状态转换规则正确")
                self.test_results.append(("状态机", "通过"))
            else:
                logger.error(f"✗ 状态机测试失败：转换结果 {[success1, success2, success3, success4, success5]}")
                self.test_results.append(("状态机", "失败"))
                
        except Exception as e:
            logger.error(f"状态机测试异常：{e}")
            self.test_results.append(("状态机", f"异常: {e}"))
    
    def test_header_validation(self):
        """测试3：表头验证"""
        logger.info("=" * 60)
        logger.info("测试3：表头验证")
        
        try:
            # 正常表头
            headers1 = [f"列{i}" for i in range(45)]
            success1 = self.header_manager.set_headers(headers1, "test_normal")
            
            # 超过最大列数
            headers2 = [f"列{i}" for i in range(100)]
            success2 = self.header_manager.set_headers(headers2, "test_exceed")
            
            # 重复表头
            headers3 = ["列1"] * 10 + ["列2"] * 10
            success3 = self.header_manager.set_headers(headers3, "test_duplicate")
            
            if success1 and not success2 and not success3:
                logger.info("✓ 表头验证测试通过：正常表头接受，异常表头拒绝")
                self.test_results.append(("表头验证", "通过"))
            else:
                logger.error(f"✗ 表头验证测试失败：结果 {[success1, success2, success3]}")
                self.test_results.append(("表头验证", "失败"))
                
        except Exception as e:
            logger.error(f"表头验证测试异常：{e}")
            self.test_results.append(("表头验证", f"异常: {e}"))
    
    def test_pagination_mode(self):
        """测试4：分页模式"""
        logger.info("=" * 60)
        logger.info("测试4：分页模式")
        
        try:
            # 设置初始表头
            headers = [f"列{i}" for i in range(45)]
            self.header_manager.set_headers(headers, "initial")
            initial_count = self.header_manager.get_header_count()
            
            # 模拟分页
            for page in range(1, 6):
                # 分页时尝试改变表头
                new_headers = [f"页{page}_列{i}" for i in range(50)]
                success = self.header_manager.update_headers_for_pagination(new_headers, page)
            
            # 检查表头是否保持不变
            final_count = self.header_manager.get_header_count()
            final_headers = self.header_manager.get_current_headers()
            
            if final_count == initial_count and final_headers == headers:
                logger.info(f"✓ 分页模式测试通过：表头保持不变 {final_count}列")
                self.test_results.append(("分页模式", "通过"))
            else:
                logger.error(f"✗ 分页模式测试失败：表头从{initial_count}变为{final_count}列")
                self.test_results.append(("分页模式", "失败"))
                
        except Exception as e:
            logger.error(f"分页模式测试异常：{e}")
            self.test_results.append(("分页模式", f"异常: {e}"))
    
    def test_cache_mechanism(self):
        """测试5：缓存机制"""
        logger.info("=" * 60)
        logger.info("测试5：缓存机制")
        
        try:
            # 清空缓存
            self.header_manager.clear_cache()
            
            # 添加多个不同的表头
            for i in range(15):
                headers = [f"表{i}_列{j}" for j in range(30)]
                self.header_manager.set_headers(headers, f"table_{i}")
            
            # 检查缓存大小（应该不超过配置的最大值）
            stats = self.header_manager.get_statistics()
            cache_size = stats.get("cache_size", 0)
            
            if cache_size <= self.header_manager.cache_size:
                logger.info(f"✓ 缓存机制测试通过：缓存大小 {cache_size} <= {self.header_manager.cache_size}")
                self.test_results.append(("缓存机制", "通过"))
            else:
                logger.error(f"✗ 缓存机制测试失败：缓存大小 {cache_size} > {self.header_manager.cache_size}")
                self.test_results.append(("缓存机制", "失败"))
                
        except Exception as e:
            logger.error(f"缓存机制测试异常：{e}")
            self.test_results.append(("缓存机制", f"异常: {e}"))
    
    def test_adapter_integration(self):
        """测试6：适配器集成"""
        logger.info("=" * 60)
        logger.info("测试6：适配器集成")
        
        try:
            # 设置表格上下文
            self.adapter.set_table_context("test_table", is_pagination=False)
            
            # 通过表格设置列数
            self.table.setColumnCount(45)
            
            # 检查是否通过管理器
            manager_count = self.header_manager.get_header_count()
            table_count = self.table.columnCount()
            
            # 模拟分页
            self.adapter.set_table_context("test_table", is_pagination=True, page=2)
            self.table.setColumnCount(45)
            
            # 再次检查
            final_manager_count = self.header_manager.get_header_count()
            final_table_count = self.table.columnCount()
            
            if manager_count == 45 and final_manager_count == 45:
                logger.info(f"✓ 适配器集成测试通过：表头通过管理器控制")
                self.test_results.append(("适配器集成", "通过"))
            else:
                logger.error(f"✗ 适配器集成测试失败：管理器{manager_count}->{final_manager_count}")
                self.test_results.append(("适配器集成", "失败"))
                
        except Exception as e:
            logger.error(f"适配器集成测试异常：{e}")
            self.test_results.append(("适配器集成", f"异常: {e}"))
    
    def test_concurrent_safety(self):
        """测试7：并发安全"""
        logger.info("=" * 60)
        logger.info("测试7：并发安全")
        
        try:
            # 清空状态
            self.header_manager.reset_headers()
            
            # 并发设置表头
            def set_headers_concurrent(thread_id):
                for i in range(10):
                    headers = [f"T{thread_id}_列{j}" for j in range(30 + thread_id)]
                    self.header_manager.set_headers(headers, f"thread_{thread_id}")
                    time.sleep(0.01)
            
            # 创建多个线程
            threads = []
            for i in range(5):
                t = threading.Thread(target=set_headers_concurrent, args=(i,))
                threads.append(t)
                t.start()
            
            # 等待完成
            for t in threads:
                t.join()
            
            # 检查最终状态
            final_count = self.header_manager.get_header_count()
            stats = self.header_manager.get_statistics()
            
            if final_count > 0 and final_count <= 50:
                logger.info(f"✓ 并发安全测试通过：最终列数 {final_count}，版本 {stats['version']}")
                self.test_results.append(("并发安全", "通过"))
            else:
                logger.error(f"✗ 并发安全测试失败：最终列数 {final_count}")
                self.test_results.append(("并发安全", "失败"))
                
        except Exception as e:
            logger.error(f"并发安全测试异常：{e}")
            self.test_results.append(("并发安全", f"异常: {e}"))
    
    def test_error_recovery(self):
        """测试8：错误恢复"""
        logger.info("=" * 60)
        logger.info("测试8：错误恢复")
        
        try:
            # 设置正常表头
            normal_headers = [f"列{i}" for i in range(30)]
            self.header_manager.set_headers(normal_headers, "normal")
            
            # 连续触发错误
            for i in range(5):
                bad_headers = [f"列{j}" for j in range(100)]  # 超过限制
                self.header_manager.set_headers(bad_headers, f"bad_{i}")
            
            # 检查是否触发重置
            stats = self.header_manager.get_statistics()
            reset_count = stats.get("reset_count", 0)
            
            if reset_count > 0:
                logger.info(f"✓ 错误恢复测试通过：触发{reset_count}次重置")
                self.test_results.append(("错误恢复", "通过"))
            else:
                logger.error(f"✗ 错误恢复测试失败：未触发重置")
                self.test_results.append(("错误恢复", "失败"))
                
        except Exception as e:
            logger.error(f"错误恢复测试异常：{e}")
            self.test_results.append(("错误恢复", f"异常: {e}"))
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始方案B架构测试")
        logger.info("=" * 60)
        
        # 设置环境
        self.setup()
        
        # 运行各项测试
        self.test_singleton_pattern()
        self.test_state_machine()
        self.test_header_validation()
        self.test_pagination_mode()
        self.test_cache_mechanism()
        self.test_adapter_integration()
        self.test_concurrent_safety()
        self.test_error_recovery()
        
        # 清理环境
        self.teardown()
        
        # 输出测试结果汇总
        logger.info("=" * 60)
        logger.info("测试结果汇总：")
        
        passed = 0
        failed = 0
        for test_name, result in self.test_results:
            if "通过" in result:
                passed += 1
                logger.info(f"  ✓ {test_name}: {result}")
            else:
                failed += 1
                logger.error(f"  ✗ {test_name}: {result}")
        
        logger.info(f"\n总计：{passed} 通过，{failed} 失败")
        
        # 输出统计信息
        logger.info("\n管理器统计信息：")
        stats = self.header_manager.get_statistics()
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        if failed == 0:
            logger.info("\n🎉 方案B架构测试全部通过！")
        else:
            logger.warning(f"\n⚠️ 方案B架构测试有 {failed} 项未通过")
        
        return failed == 0


def main():
    """主测试入口"""
    try:
        tester = TestSolutionB()
        success = tester.run_all_tests()
        
        sys.exit(0 if success else 1)
        
    except Exception as e:
        logger.error(f"测试执行失败：{e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()